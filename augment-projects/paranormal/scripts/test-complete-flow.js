#!/usr/bin/env node

// Complete registration flow test with phone verification bypass
// This script tests the entire user journey from registration to verified user

const BASE_URL = 'http://localhost:3000';

async function testCompleteFlow() {
  console.log('🧪 Testing Complete Registration Flow with Phone Verification Bypass...\n');
  
  try {
    // Test data
    const testUser = {
      firstName: 'Complete',
      lastName: 'Flow',
      email: '<EMAIL>',
      phone: '+995555111222',
      facebookUrl: 'https://facebook.com/completeflow',
      gender: 'other',
      age: 32,
      password: 'CompleteFlow123',
      confirmPassword: 'CompleteFlow123'
    };
    
    // Step 1: Register new user
    console.log('1️⃣ Step 1: User Registration...');
    const registerResponse = await fetch(`${BASE_URL}/api/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testUser)
    });
    
    const registerData = await registerResponse.json();
    console.log('Registration result:', {
      success: registerData.success,
      requiresVerification: registerData.requiresVerification,
      userVerified: registerData.user?.isVerified
    });
    
    if (!registerData.success) {
      throw new Error(`Registration failed: ${registerData.message}`);
    }
    
    const token = registerData.token;
    console.log('✅ User registered successfully (unverified)\n');
    
    // Step 2: Check initial verification status
    console.log('2️⃣ Step 2: Check initial verification status...');
    const initialMeResponse = await fetch(`${BASE_URL}/api/auth/me`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    const initialMeData = await initialMeResponse.json();
    console.log('Initial user status:', {
      success: initialMeData.success,
      isVerified: initialMeData.user?.isVerified,
      name: initialMeData.user?.name
    });
    
    if (initialMeData.user?.isVerified) {
      throw new Error('User should not be verified initially');
    }
    console.log('✅ User is correctly unverified initially\n');
    
    // Step 3: Attempt phone verification with development bypass
    console.log('3️⃣ Step 3: Phone verification with development bypass...');
    console.log('Using any 6-digit code (development bypass active)');
    
    const verifyResponse = await fetch(`${BASE_URL}/api/auth/verify-phone`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ code: '999888' }) // Any 6-digit code works in development
    });
    
    const verifyData = await verifyResponse.json();
    console.log('Verification result:', {
      success: verifyData.success,
      userVerified: verifyData.user?.isVerified,
      message: verifyData.message
    });
    
    if (!verifyData.success) {
      throw new Error(`Phone verification failed: ${verifyData.message}`);
    }
    
    const newToken = verifyData.token; // Updated token with verification status
    console.log('✅ Phone verification successful via development bypass\n');
    
    // Step 4: Verify user is now verified
    console.log('4️⃣ Step 4: Confirm verification status updated...');
    const verifiedMeResponse = await fetch(`${BASE_URL}/api/auth/me`, {
      headers: {
        'Authorization': `Bearer ${newToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    const verifiedMeData = await verifiedMeResponse.json();
    console.log('Updated user status:', {
      success: verifiedMeData.success,
      isVerified: verifiedMeData.user?.isVerified,
      name: verifiedMeData.user?.name,
      balance: verifiedMeData.user?.balance
    });
    
    if (!verifiedMeData.user?.isVerified) {
      throw new Error('User should be verified after successful verification');
    }
    console.log('✅ User verification status correctly updated\n');
    
    // Step 5: Test profile update as verified user
    console.log('5️⃣ Step 5: Test profile update as verified user...');
    const profileUpdateData = {
      firstName: 'Verified',
      lastName: 'User',
      email: '<EMAIL>',
      facebookUrl: 'https://facebook.com/verifieduser',
      gender: 'male',
      age: 35
    };
    
    const profileUpdateResponse = await fetch(`${BASE_URL}/api/auth/profile`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${newToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(profileUpdateData)
    });
    
    const profileUpdateResult = await profileUpdateResponse.json();
    console.log('Profile update result:', {
      success: profileUpdateResult.success,
      newName: profileUpdateResult.user?.name,
      stillVerified: profileUpdateResult.user?.isVerified
    });
    
    if (!profileUpdateResult.success) {
      throw new Error(`Profile update failed: ${profileUpdateResult.message}`);
    }
    console.log('✅ Profile updated successfully while maintaining verification\n');
    
    // Step 6: Test login with updated credentials
    console.log('6️⃣ Step 6: Test login with updated email...');
    const loginResponse = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: profileUpdateData.email,
        password: testUser.password
      })
    });
    
    const loginData = await loginResponse.json();
    console.log('Login result:', {
      success: loginData.success,
      userName: loginData.user?.name,
      isVerified: loginData.user?.isVerified,
      balance: loginData.user?.balance
    });
    
    if (!loginData.success) {
      throw new Error(`Login failed: ${loginData.message}`);
    }
    console.log('✅ Login successful with updated credentials\n');
    
    // Step 7: Test validation (invalid verification code format)
    console.log('7️⃣ Step 7: Test validation with invalid code format...');
    const invalidCodeResponse = await fetch(`${BASE_URL}/api/auth/verify-phone`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${loginData.token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ code: '12345' }) // Only 5 digits - should fail validation
    });
    
    const invalidCodeData = await invalidCodeResponse.json();
    console.log('Invalid code test result:', {
      success: invalidCodeData.success,
      hasErrors: !!invalidCodeData.errors
    });
    
    if (invalidCodeData.success) {
      throw new Error('Invalid code format should have been rejected');
    }
    console.log('✅ Input validation working correctly\n');
    
    console.log('🎉 Complete registration flow test passed!');
    console.log('\n📋 Flow Summary:');
    console.log('✅ User Registration (unverified)');
    console.log('✅ Initial Status Check (unverified)');
    console.log('✅ Phone Verification (development bypass)');
    console.log('✅ Verification Status Update (verified)');
    console.log('✅ Profile Update (as verified user)');
    console.log('✅ Login with Updated Credentials');
    console.log('✅ Input Validation');
    
    console.log('\n🔧 Development Features Tested:');
    console.log('✅ Phone verification bypass (any 6-digit code)');
    console.log('✅ Proper verification status updates');
    console.log('✅ Token refresh after verification');
    console.log('✅ Complete user journey end-to-end');
    
  } catch (error) {
    console.error('❌ Complete flow test failed:', error.message);
    process.exit(1);
  }
}

// Run the test
testCompleteFlow();
