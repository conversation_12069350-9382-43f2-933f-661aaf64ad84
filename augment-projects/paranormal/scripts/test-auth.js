#!/usr/bin/env node

// Authentication system test script
// This script tests the authentication endpoints to ensure they're working correctly

const BASE_URL = 'http://localhost:3000';

async function testAuthSystem() {
  console.log('🧪 Testing Authentication System...\n');
  
  try {
    // Test data
    const testUser = {
      firstName: 'Test',
      lastName: 'User',
      email: '<EMAIL>',
      phone: '+995555123456',
      facebookUrl: 'https://facebook.com/testuser',
      gender: 'male',
      age: 25,
      password: 'TestPassword123',
      confirmPassword: 'TestPassword123'
    };
    
    // Test 1: Registration
    console.log('1️⃣ Testing user registration...');
    const registerResponse = await fetch(`${BASE_URL}/api/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testUser)
    });
    
    const registerData = await registerResponse.json();
    console.log('Registration response:', registerData);
    
    if (!registerData.success) {
      throw new Error(`Registration failed: ${registerData.message}`);
    }
    
    const token = registerData.token;
    const userId = registerData.user.id;
    console.log('✅ Registration successful\n');
    
    // Test 2: Get current user
    console.log('2️⃣ Testing get current user...');
    const meResponse = await fetch(`${BASE_URL}/api/auth/me`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    const meData = await meResponse.json();
    console.log('Current user response:', meData);
    
    if (!meData.success) {
      throw new Error(`Get current user failed: ${meData.message}`);
    }
    console.log('✅ Get current user successful\n');
    
    // Test 3: Phone verification (with mock code)
    console.log('3️⃣ Testing phone verification...');
    const verifyResponse = await fetch(`${BASE_URL}/api/auth/verify-phone`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ code: '123456' }) // This will fail, but we can test the endpoint
    });
    
    const verifyData = await verifyResponse.json();
    console.log('Phone verification response:', verifyData);
    
    if (verifyData.success) {
      console.log('✅ Phone verification successful\n');
    } else {
      console.log('⚠️ Phone verification failed (expected with mock code)\n');
    }
    
    // Test 4: Login
    console.log('4️⃣ Testing user login...');
    const loginResponse = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: testUser.email,
        password: testUser.password
      })
    });
    
    const loginData = await loginResponse.json();
    console.log('Login response:', loginData);
    
    if (!loginData.success) {
      throw new Error(`Login failed: ${loginData.message}`);
    }
    console.log('✅ Login successful\n');
    
    // Test 5: Logout
    console.log('5️⃣ Testing user logout...');
    const logoutResponse = await fetch(`${BASE_URL}/api/auth/logout`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${loginData.token}`,
        'Content-Type': 'application/json'
      }
    });
    
    const logoutData = await logoutResponse.json();
    console.log('Logout response:', logoutData);
    
    if (!logoutData.success) {
      throw new Error(`Logout failed: ${logoutData.message}`);
    }
    console.log('✅ Logout successful\n');
    
    console.log('🎉 All authentication tests passed!');
    console.log('\n📋 Test Summary:');
    console.log('✅ User Registration');
    console.log('✅ Get Current User');
    console.log('⚠️ Phone Verification (mock)');
    console.log('✅ User Login');
    console.log('✅ User Logout');
    
  } catch (error) {
    console.error('❌ Authentication test failed:', error.message);
    process.exit(1);
  }
}

// Run the test
testAuthSystem();
