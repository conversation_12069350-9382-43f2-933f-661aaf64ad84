<!DOCTYPE html>
<html lang="ka">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ბუნებაში გასვლა - პარანორმალური მოვლენების მკვლევარი</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        :root {
            --primary: #2d5a27;
            --primary-dark: #1a3318;
            --accent: #8b4513;
            --accent-light: #cd853f;
            --mystery: #4a0e4e;
            --mystery-light: #8b5a8c;
        }

        .dark {
            --primary: #4a7c59;
            --primary-dark: #2d5a27;
            --accent: #cd853f;
            --accent-light: #daa520;
            --mystery: #8b5a8c;
            --mystery-light: #ba68c8;
        }

        /* Custom animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes pulse-glow {
            0%, 100% {
                box-shadow: 0 0 5px rgba(139, 69, 19, 0.5);
            }
            50% {
                box-shadow: 0 0 20px rgba(139, 69, 19, 0.8);
            }
        }

        .animate-fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        .animate-slide-in-left {
            animation: slideInLeft 0.6s ease-out;
        }

        .animate-pulse-glow {
            animation: pulse-glow 2s infinite;
        }

        .bg-primary { background-color: var(--primary); }
        .bg-primary-dark { background-color: var(--primary-dark); }
        .bg-accent { background-color: var(--accent); }
        .bg-accent-light { background-color: var(--accent-light); }
        .text-primary { color: var(--primary); }
        .text-accent { color: var(--accent); }
        .text-accent-light { color: var(--accent-light); }
        .border-accent { border-color: var(--accent); }

        .hover\:bg-primary:hover { background-color: var(--primary); }
        .hover\:bg-primary-dark:hover { background-color: var(--primary-dark); }
        .hover\:bg-accent:hover { background-color: var(--accent); }

        /* Smooth transitions */
        * {
            transition: all 0.3s ease;
        }
    </style>
</head>
<body class="bg-white dark:bg-gray-900 text-gray-900 dark:text-white">
    <!-- Theme Toggle -->
    <button id="themeToggle" class="fixed top-6 right-6 z-50 p-3 rounded-full bg-white/20 dark:bg-black/20 backdrop-blur-sm border border-white/30 dark:border-gray-600 transition-all duration-300 hover:scale-110 hover:bg-white/30 dark:hover:bg-black/30">
        <svg id="sunIcon" class="w-6 h-6 text-yellow-400 hidden" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd" />
        </svg>
        <svg id="moonIcon" class="w-6 h-6 text-gray-800" fill="currentColor" viewBox="0 0 20 20">
            <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
        </svg>
    </button>

    <!-- Hero Section -->
    <section class="relative min-h-screen flex items-center justify-center overflow-hidden">
        <!-- Background Image -->
        <div class="absolute inset-0 z-0">
            <img src="./public/images/scary-histories.jpg" alt="საშიში ისტორიები ბუნებაში" class="w-full h-full object-cover">
            <!-- Dark overlay for better text readability -->
            <div class="absolute inset-0 bg-black/50 dark:bg-black/70"></div>
        </div>

        <!-- Content -->
        <div class="relative z-10 text-center px-4 max-w-4xl mx-auto animate-fade-in-up">
            <h1 class="text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight">
                ბუნებაში გასვლა
                <span class="block text-2xl md:text-3xl lg:text-4xl text-accent-light mt-2">
                    პარანორმალური მოვლენების მკვლევარი
                </span>
            </h1>
            
            <p class="text-lg md:text-xl text-gray-200 mb-8 max-w-2xl mx-auto leading-relaxed">
                შემოუერთდით ჩვენს ბუნებაში გასვლებს, სადაც ერთიანდება ბუნებრივი სილამაზე, 
                ადამიანური ურთიერთობები და საშიში ისტორიების მოყოლა კოცონის შუქზე
            </p>

            <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <button onclick="scrollToTrips()" class="bg-primary hover:bg-primary-dark text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 transform hover:scale-105 animate-pulse-glow">
                    გასვლების ნახვა
                </button>
                
                <button class="border-2 border-accent text-accent hover:bg-accent hover:text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 transform hover:scale-105">
                    ჩვენს შესახებ
                </button>
            </div>

            <!-- Scroll indicator -->
            <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
                <div class="w-6 h-10 border-2 border-white rounded-full flex justify-center">
                    <div class="w-1 h-3 bg-white rounded-full mt-2 animate-pulse"></div>
                </div>
            </div>
        </div>

        <!-- Decorative elements -->
        <div class="absolute top-20 left-10 w-20 h-20 border border-accent/30 rounded-full animate-pulse hidden lg:block"></div>
        <div class="absolute bottom-20 right-10 w-16 h-16 border border-gray-400/30 rounded-full animate-pulse hidden lg:block"></div>
    </section>

    <!-- Photo Showcase Section -->
    <section class="py-16 px-4 bg-gradient-to-br from-purple-100/50 to-orange-100/50 dark:from-purple-900/20 dark:to-orange-900/20">
        <div class="max-w-7xl mx-auto">
            <!-- Section Header -->
            <div class="text-center mb-12 animate-fade-in-up">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-800 dark:text-white mb-4">
                    ჩვენი გასვლების ატმოსფერო
                </h2>
                <p class="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                    ნახეთ როგორ ატარებენ დროს ჩვენი მონაწილეები ბუნებაში გასვლებზე
                </p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <!-- Image Showcase -->
                <div class="animate-slide-in-left">
                    <div class="relative h-96 rounded-2xl overflow-hidden shadow-2xl">
                        <img id="showcaseImage" src="./public/images/picnic.jpg" alt="ბუნებაში პიკნიკი" class="w-full h-full object-cover transition-all duration-500">

                        <!-- Navigation Arrows -->
                        <button onclick="prevShowcaseImage()" class="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-all duration-300">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                            </svg>
                        </button>

                        <button onclick="nextShowcaseImage()" class="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-all duration-300">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                        </button>

                        <!-- Image Info Overlay -->
                        <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6">
                            <h3 id="showcaseTitle" class="text-xl font-bold text-white mb-2">
                                ბუნებაში პიკნიკი
                            </h3>
                            <p id="showcaseDescription" class="text-gray-200">
                                ბუნებრივ გარემოში ერთად გატარებული დრო
                            </p>
                        </div>
                    </div>

                    <!-- Dots Indicator -->
                    <div class="flex justify-center mt-6 space-x-2">
                        <button onclick="goToShowcaseImage(0)" class="showcase-dot w-3 h-3 rounded-full transition-all duration-300 bg-accent scale-125"></button>
                        <button onclick="goToShowcaseImage(1)" class="showcase-dot w-3 h-3 rounded-full transition-all duration-300 bg-gray-300 dark:bg-gray-600"></button>
                        <button onclick="goToShowcaseImage(2)" class="showcase-dot w-3 h-3 rounded-full transition-all duration-300 bg-gray-300 dark:bg-gray-600"></button>
                        <button onclick="goToShowcaseImage(3)" class="showcase-dot w-3 h-3 rounded-full transition-all duration-300 bg-gray-300 dark:bg-gray-600"></button>
                    </div>
                </div>

                <!-- Content -->
                <div class="space-y-6 animate-fade-in-up" style="animation-delay: 0.3s">
                    <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl p-6 shadow-lg">
                        <h3 class="text-2xl font-bold text-gray-800 dark:text-white mb-4">
                            რატომ ჩვენი გასვლები?
                        </h3>
                        <ul class="space-y-3 text-gray-600 dark:text-gray-300">
                            <li class="flex items-start gap-3">
                                <span class="text-accent text-xl">🌲</span>
                                <span>ბუნებრივ გარემოში უნიკალური გამოცდილება</span>
                            </li>
                            <li class="flex items-start gap-3">
                                <span class="text-accent text-xl">👥</span>
                                <span>მეგობრული და უსაფრთხო ატმოსფერო</span>
                            </li>
                            <li class="flex items-start gap-3">
                                <span class="text-accent text-xl">🔥</span>
                                <span>კოცონის შუქზე საშიში ისტორიები</span>
                            </li>
                            <li class="flex items-start gap-3">
                                <span class="text-accent text-xl">📸</span>
                                <span>დაუვიწყარი მოგონებები და ფოტოები</span>
                            </li>
                        </ul>
                    </div>

                    <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl p-6 shadow-lg">
                        <h4 class="text-xl font-semibold text-gray-800 dark:text-white mb-3">
                            რას ვთავაზობთ:
                        </h4>
                        <div class="grid grid-cols-2 gap-4 text-sm text-gray-600 dark:text-gray-300">
                            <div class="flex items-center gap-2">
                                <span class="w-2 h-2 bg-primary rounded-full"></span>
                                <span>ტრანსპორტი</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <span class="w-2 h-2 bg-primary rounded-full"></span>
                                <span>კვება</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <span class="w-2 h-2 bg-primary rounded-full"></span>
                                <span>ღია ცეცხლი</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <span class="w-2 h-2 bg-primary rounded-full"></span>
                                <span>ფოტო/ვიდეო</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <span class="w-2 h-2 bg-primary rounded-full"></span>
                                <span>გიდი</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <span class="w-2 h-2 bg-primary rounded-full"></span>
                                <span>უსაფრთხოება</span>
                            </div>
                        </div>
                    </div>

                    <div class="text-center">
                        <button onclick="scrollToTrips()" class="bg-accent hover:bg-accent-light text-white px-8 py-3 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg">
                            გასვლების ნახვა
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Trips Section -->
    <section id="trips-section" class="py-16 px-4 bg-gray-50 dark:bg-gray-900">
        <div class="max-w-7xl mx-auto">
            <!-- Section Header -->
            <div class="text-center mb-12 animate-fade-in-up">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-800 dark:text-white mb-4">
                    ბუნებაში გასვლები
                </h2>
                <p class="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                    შემოუერთდით ჩვენს ბუნებაში გასვლებს და გაიცანით პარანორმალური მოვლენების საიდუმლოებები
                </p>
            </div>

            <!-- All Trips -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Trip Card 1 -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden transition-all duration-300 transform hover:scale-105 hover:shadow-2xl animate-slide-in-left">
                    <div class="relative h-48 overflow-hidden">
                        <img src="./public/images/picnic.jpg" alt="ბორჯომის ტყეში საშიში ისტორიები" class="w-full h-full object-cover transition-transform duration-300 hover:scale-110">
                        
                        <!-- Participants Counter -->
                        <div class="absolute top-4 right-4 bg-black/70 text-white px-3 py-1 rounded-full text-sm font-semibold">
                            8/15
                        </div>

                        <!-- Status Badge -->
                        <div class="absolute top-4 left-4 bg-yellow-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                            ივსება
                        </div>
                    </div>

                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-800 dark:text-white mb-2">
                            ბორჯომის ტყეში საშიში ისტორიები
                        </h3>
                        
                        <p class="text-gray-600 dark:text-gray-300 mb-4 text-sm">
                            ბორჯომის ტყის ღრმა ნაწილში გასვლა, სადაც მზის ჩასვლისას მოვისმენთ ძველ ლეგენდებს და საშიშ ისტორიებს
                        </p>

                        <!-- Date and Location -->
                        <div class="flex items-center gap-4 mb-4 text-sm text-gray-500 dark:text-gray-400">
                            <div class="flex items-center gap-1">
                                <span>📅</span>
                                <span>2024-03-15</span>
                            </div>
                            <div class="flex items-center gap-1">
                                <span>📍</span>
                                <span>ბორჯომი</span>
                            </div>
                        </div>

                        <!-- Progress Bar -->
                        <div class="mb-4">
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                                    მონაწილეობა
                                </span>
                                <span class="text-sm text-gray-500 dark:text-gray-400">
                                    53%
                                </span>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div class="h-2 rounded-full bg-yellow-500 transition-all duration-500" style="width: 53%"></div>
                            </div>
                        </div>

                        <!-- Price -->
                        <div class="flex items-center justify-between mb-4">
                            <span class="text-lg font-bold text-primary">
                                50 ₾
                            </span>
                        </div>

                        <!-- Action Button -->
                        <button class="w-full py-3 px-4 rounded-lg font-semibold transition-all duration-300 bg-primary hover:bg-primary-dark text-white transform hover:scale-105">
                            რეგისტრაცია
                        </button>
                    </div>
                </div>

                <!-- Trip Card 2 -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden transition-all duration-300 transform hover:scale-105 hover:shadow-2xl animate-slide-in-left" style="animation-delay: 0.1s">
                    <div class="relative h-48 overflow-hidden">
                        <img src="./public/images/activities.jpg" alt="კაზბეგის მისტიური ღამე" class="w-full h-full object-cover transition-transform duration-300 hover:scale-110">
                        
                        <!-- Participants Counter -->
                        <div class="absolute top-4 right-4 bg-black/70 text-white px-3 py-1 rounded-full text-sm font-semibold">
                            3/12
                        </div>

                        <!-- Status Badge -->
                        <div class="absolute top-4 left-4 bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                            ღია რეგისტრაცია
                        </div>
                    </div>

                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-800 dark:text-white mb-2">
                            კაზბეგის მისტიური ღამე
                        </h3>
                        
                        <p class="text-gray-600 dark:text-gray-300 mb-4 text-sm">
                            კაზბეგთან ახლოს კემპინგი, ღამის ცის ქვეშ პარანორმალური მოვლენების შესწავლა
                        </p>

                        <!-- Date and Location -->
                        <div class="flex items-center gap-4 mb-4 text-sm text-gray-500 dark:text-gray-400">
                            <div class="flex items-center gap-1">
                                <span>📅</span>
                                <span>2024-03-22</span>
                            </div>
                            <div class="flex items-center gap-1">
                                <span>📍</span>
                                <span>კაზბეგი</span>
                            </div>
                        </div>

                        <!-- Progress Bar -->
                        <div class="mb-4">
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                                    მონაწილეობა
                                </span>
                                <span class="text-sm text-gray-500 dark:text-gray-400">
                                    25%
                                </span>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div class="h-2 rounded-full bg-green-500 transition-all duration-500" style="width: 25%"></div>
                            </div>
                        </div>

                        <!-- Price -->
                        <div class="flex items-center justify-between mb-4">
                            <span class="text-lg font-bold text-primary">
                                80 ₾
                            </span>
                        </div>

                        <!-- Action Button -->
                        <button class="w-full py-3 px-4 rounded-lg font-semibold transition-all duration-300 bg-primary hover:bg-primary-dark text-white transform hover:scale-105">
                            რეგისტრაცია
                        </button>
                    </div>
                </div>

                <!-- Trip Card 3 - Completed -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden transition-all duration-300 transform hover:scale-105 hover:shadow-2xl animate-slide-in-left cursor-pointer" style="animation-delay: 0.2s">
                    <div class="relative h-48 overflow-hidden">
                        <img src="./public/images/paranormal.jpg" alt="ვარძიის გამოქვაბულები და ლეგენდები" class="w-full h-full object-cover transition-transform duration-300 hover:scale-110">
                        
                        <!-- Participants Counter -->
                        <div class="absolute top-4 right-4 bg-black/70 text-white px-3 py-1 rounded-full text-sm font-semibold">
                            10/10
                        </div>

                        <!-- Status Badge -->
                        <div class="absolute top-4 left-4 bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                            დასრულებული ✊
                        </div>
                    </div>

                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-800 dark:text-white mb-2">
                            ვარძიის გამოქვაბულები და ლეგენდები
                        </h3>
                        
                        <p class="text-gray-600 dark:text-gray-300 mb-4 text-sm">
                            ვარძიის ისტორიული კომპლექსი და მისი საიდუმლო ისტორიები კოცონის შუქზე
                        </p>

                        <!-- Date and Location -->
                        <div class="flex items-center gap-4 mb-4 text-sm text-gray-500 dark:text-gray-400">
                            <div class="flex items-center gap-1">
                                <span>📅</span>
                                <span>2024-02-28</span>
                            </div>
                            <div class="flex items-center gap-1">
                                <span>📍</span>
                                <span>ვარძია</span>
                            </div>
                        </div>

                        <!-- Progress Bar -->
                        <div class="mb-4">
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                                    მონაწილეობა
                                </span>
                                <span class="text-sm text-gray-500 dark:text-gray-400">
                                    100%
                                </span>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div class="h-2 rounded-full bg-blue-500 transition-all duration-500" style="width: 100%"></div>
                            </div>
                        </div>

                        <!-- Price -->
                        <div class="flex items-center justify-between mb-4">
                            <span class="text-lg font-bold text-primary">
                                60 ₾
                            </span>
                            <span class="text-sm text-blue-600 dark:text-blue-400 font-medium">
                                ფოტო/ვიდეო ხელმისაწვდომია
                            </span>
                        </div>

                        <!-- Action Button -->
                        <button class="w-full py-3 px-4 rounded-lg font-semibold transition-all duration-300 bg-blue-500 hover:bg-blue-600 text-white transform hover:scale-105">
                            ფოტო/ვიდეოს ნახვა
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-800 dark:bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div>
                    <h3 class="text-xl font-bold mb-4">ბუნებაში გასვლა</h3>
                    <p class="text-gray-300">
                        პარანორმალური მოვლენების მკვლევარი და ბუნებაში გასვლების ორგანიზატორი
                    </p>
                </div>
                
                <div>
                    <h4 class="text-lg font-semibold mb-4">კონტაქტი</h4>
                    <div class="space-y-2 text-gray-300">
                        <p>📧 <EMAIL></p>
                        <p>📱 +995 555 123 456</p>
                        <p>📍 თბილისი, საქართველო</p>
                    </div>
                </div>
                
                <div>
                    <h4 class="text-lg font-semibold mb-4">გამოგვყევით</h4>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-300 hover:text-white transition-colors">
                            Facebook
                        </a>
                        <a href="#" class="text-gray-300 hover:text-white transition-colors">
                            YouTube
                        </a>
                        <a href="#" class="text-gray-300 hover:text-white transition-colors">
                            Instagram
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 ბუნებაში გასვლა. ყველა უფლება დაცულია.</p>
            </div>
        </div>
    </footer>

    <script>
        // Theme Toggle Functionality
        const themeToggle = document.getElementById('themeToggle');
        const sunIcon = document.getElementById('sunIcon');
        const moonIcon = document.getElementById('moonIcon');
        const html = document.documentElement;

        // Check for saved theme preference or default to 'light'
        const currentTheme = localStorage.getItem('theme') || 'light';
        
        if (currentTheme === 'dark') {
            html.classList.add('dark');
            sunIcon.classList.remove('hidden');
            moonIcon.classList.add('hidden');
        } else {
            html.classList.remove('dark');
            sunIcon.classList.add('hidden');
            moonIcon.classList.remove('hidden');
        }

        themeToggle.addEventListener('click', () => {
            if (html.classList.contains('dark')) {
                html.classList.remove('dark');
                localStorage.setItem('theme', 'light');
                sunIcon.classList.add('hidden');
                moonIcon.classList.remove('hidden');
            } else {
                html.classList.add('dark');
                localStorage.setItem('theme', 'dark');
                sunIcon.classList.remove('hidden');
                moonIcon.classList.add('hidden');
            }
        });

        // Smooth scroll to trips section
        function scrollToTrips() {
            document.getElementById('trips-section').scrollIntoView({
                behavior: 'smooth'
            });
        }

        // Photo Showcase Functionality
        const showcaseImages = [
            {
                src: './public/images/picnic.jpg',
                title: 'ბუნებაში პიკნიკი',
                description: 'ბუნებრივ გარემოში ერთად გატარებული დრო'
            },
            {
                src: './public/images/activities.jpg',
                title: 'ჯგუფური აქტივობები',
                description: 'სხვადასხვა თამაშები და გართობა'
            },
            {
                src: './public/images/paranormal.jpg',
                title: 'პარანორმალური კვლევა',
                description: 'საიდუმლოებების შესწავლა'
            },
            {
                src: './public/images/register-modal.jpg',
                title: 'ღამის ატმოსფერო',
                description: 'კოცონის შუქზე საშიში ისტორიები'
            }
        ];

        let currentShowcaseIndex = 0;

        function updateShowcaseImage() {
            const image = document.getElementById('showcaseImage');
            const title = document.getElementById('showcaseTitle');
            const description = document.getElementById('showcaseDescription');
            const dots = document.querySelectorAll('.showcase-dot');

            image.src = showcaseImages[currentShowcaseIndex].src;
            image.alt = showcaseImages[currentShowcaseIndex].title;
            title.textContent = showcaseImages[currentShowcaseIndex].title;
            description.textContent = showcaseImages[currentShowcaseIndex].description;

            // Update dots
            dots.forEach((dot, index) => {
                if (index === currentShowcaseIndex) {
                    dot.classList.remove('bg-gray-300', 'dark:bg-gray-600');
                    dot.classList.add('bg-accent', 'scale-125');
                } else {
                    dot.classList.remove('bg-accent', 'scale-125');
                    dot.classList.add('bg-gray-300', 'dark:bg-gray-600');
                }
            });
        }

        function nextShowcaseImage() {
            currentShowcaseIndex = (currentShowcaseIndex + 1) % showcaseImages.length;
            updateShowcaseImage();
        }

        function prevShowcaseImage() {
            currentShowcaseIndex = currentShowcaseIndex === 0 ? showcaseImages.length - 1 : currentShowcaseIndex - 1;
            updateShowcaseImage();
        }

        function goToShowcaseImage(index) {
            currentShowcaseIndex = index;
            updateShowcaseImage();
        }

        // Auto-rotate showcase images
        setInterval(nextShowcaseImage, 5000);
    </script>
</body>
</html>
