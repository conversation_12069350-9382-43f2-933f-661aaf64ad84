import Navigation from './components/Navigation';
import HeroSection from './components/HeroSection';
import PhotoShowcase from './components/PhotoShowcase';
import TripsSection from './components/TripsSection';

export default function Home() {
  return (
    <main className="min-h-screen">
      {/* Navigation */}
      <Navigation />

      {/* Hero Section */}
      <HeroSection />

      {/* Photo Showcase */}
      <PhotoShowcase />

      {/* Trips Section */}
      <TripsSection />

      {/* About Section */}
      <section id="about" className="py-16 px-4 bg-gray-50 dark:bg-gray-900">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-800 dark:text-white mb-8">
            ჩვენს შესახებ
          </h2>
          <div className="prose prose-lg dark:prose-invert mx-auto">
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              ჩვენ ვართ პარანორმალური მოვლენების მკვლევარები და ბუნებაში გასვლების ორგანიზატორები.
              ჩვენი მიზანია შევქმნათ უნიკალური გამოცდილება, სადაც ერთიანდება ბუნებრივი სილამაზე,
              ადამიანური ურთიერთობები და საიდუმლოებების შესწავლა.
            </p>
            <p className="text-gray-600 dark:text-gray-300">
              ყოველი გასვლა არის ახალი თავგადასავალი, სადაც მონაწილეები იღებენ შანსს
              გაეცნონ ერთმანეთს, გაიზიარონ საშიში ისტორიები და შექმნან დაუვიწყარი მოგონებები.
            </p>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer id="contact" className="bg-gray-800 dark:bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
              <h3 className="text-xl font-bold mb-4">ბუნებაში გასვლა</h3>
              <p className="text-gray-300">
                პარანორმალური მოვლენების მკვლევარი და ბუნებაში გასვლების ორგანიზატორი
              </p>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">კონტაქტი</h4>
              <div className="space-y-2 text-gray-300">
                <p>📧 <EMAIL></p>
                <p>📱 +995 555 123 456</p>
                <p>📍 თბილისი, საქართველო</p>
              </div>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">გამოგვყევით</h4>
              <div className="flex space-x-4">
                <a href="#" className="text-gray-300 hover:text-white transition-colors">
                  Facebook
                </a>
                <a href="#" className="text-gray-300 hover:text-white transition-colors">
                  YouTube
                </a>
                <a href="#" className="text-gray-300 hover:text-white transition-colors">
                  Instagram
                </a>
              </div>
            </div>
          </div>

          <div className="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 ბუნებაში გასვლა. ყველა უფლება დაცულია.</p>
          </div>
        </div>
      </footer>
    </main>
  );
}
