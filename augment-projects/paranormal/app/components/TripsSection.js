'use client';

import { useState, useEffect } from 'react';
import TripCard from './TripCard';

export default function TripsSection() {
  const [trips, setTrips] = useState([]);
  const [isVisible, setIsVisible] = useState(false);

  // Mock data - ეს მერე API-დან მოვა
  useEffect(() => {
    const mockTrips = [
      {
        id: 1,
        title: "ბორჯომის ტყეში საშიში ისტორიები",
        description: "ბორჯომის ტყის ღრმა ნაწილში გასვლა, სადაც მზის ჩასვლისას მოვისმენთ ძველ ლეგენდებს და საშიშ ისტორიებს",
        image: "/images/picnic.jpg",
        date: "2024-03-15",
        location: "ბორჯომი",
        price: 50,
        currentParticipants: 8,
        maxParticipants: 15,
        status: "filling"
      },
      {
        id: 2,
        title: "კაზბეგის მისტიური ღამე",
        description: "კაზბეგთან ახლოს კემპინგი, ღამის ცის ქვეშ პარანორმალური მოვლენების შესწავლა",
        image: "/images/activities.jpg",
        date: "2024-03-22",
        location: "კაზბეგი",
        price: 80,
        currentParticipants: 3,
        maxParticipants: 12,
        status: "open"
      },
      {
        id: 3,
        title: "ვარძიის გამოქვაბულები და ლეგენდები",
        description: "ვარძიის ისტორიული კომპლექსი და მისი საიდუმლო ისტორიები კოცონის შუქზე",
        image: "/images/paranormal.jpg",
        date: "2024-02-28",
        location: "ვარძია",
        price: 60,
        currentParticipants: 10,
        maxParticipants: 10,
        status: "completed"
      },
      {
        id: 4,
        title: "მცხეთის ძველი ტაძრები",
        description: "მცხეთის ისტორიული ძეგლები და მათთან დაკავშირებული მისტიური ისტორიები",
        image: "/images/register-modal.jpg",
        date: "2024-04-05",
        location: "მცხეთა",
        price: 45,
        currentParticipants: 15,
        maxParticipants: 15,
        status: "full"
      }
    ];

    setTrips(mockTrips);
    setIsVisible(true);
  }, []);

  return (
    <section id="trips-section" className="py-16 px-4 bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <div className={`text-center mb-12 ${isVisible ? 'animate-fade-in-up' : 'opacity-0'}`}>
          <h2 className="text-3xl md:text-4xl font-bold text-gray-800 dark:text-white mb-4">
            ბუნებაში გასვლები
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            შემოუერთდით ჩვენს ბუნებაში გასვლებს და გაიცანით პარანორმალური მოვლენების საიდუმლოებები
          </p>
        </div>

        {/* All Trips */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {trips.map((trip, index) => (
            <div
              key={trip.id}
              className={`${isVisible ? 'animate-slide-in-left' : 'opacity-0'}`}
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <TripCard trip={trip} />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
