'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';

export default function PhotoShowcase() {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isVisible, setIsVisible] = useState(false);

  // ფოტოები რომლებიც გამოიყენება ფორმატირებისთვის
  const showcaseImages = [
    {
      src: '/images/picnic.jpg',
      title: 'ბუნებაში პიკნიკი',
      description: 'ბუნებრივ გარემოში ერთად გატარებული დრო'
    },
    {
      src: '/images/activities.jpg',
      title: 'ჯგუფური აქტივობები',
      description: 'სხვადასხვა თამაშები და გართობა'
    },
    {
      src: '/images/paranormal.jpg',
      title: 'პარანორმალური კვლევა',
      description: 'საიდუმლოებების შესწავლა'
    },
    {
      src: '/images/register-modal.jpg',
      title: 'ღამის ატმოსფერო',
      description: 'კოცონის შუქზე საშიში ისტორიები'
    }
  ];

  useEffect(() => {
    setIsVisible(true);
    
    // Auto-rotate images every 5 seconds
    const interval = setInterval(() => {
      setCurrentImageIndex((prev) => (prev + 1) % showcaseImages.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [showcaseImages.length]);

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % showcaseImages.length);
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) => 
      prev === 0 ? showcaseImages.length - 1 : prev - 1
    );
  };

  const goToImage = (index) => {
    setCurrentImageIndex(index);
  };

  return (
    <section id="photos" className="py-16 px-4 bg-gradient-to-br from-mystery/10 to-accent/10 dark:from-mystery/20 dark:to-accent/20">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <div className={`text-center mb-12 ${isVisible ? 'animate-fade-in-up' : 'opacity-0'}`}>
          <h2 className="text-3xl md:text-4xl font-bold text-gray-800 dark:text-white mb-4">
            ჩვენი გასვლების ატმოსფერო
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            ნახეთ როგორ ატარებენ დროს ჩვენი მონაწილეები ბუნებაში გასვლებზე
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Image Carousel */}
          <div className={`relative ${isVisible ? 'animate-slide-in-left' : 'opacity-0'}`}>
            <div className="relative h-96 rounded-2xl overflow-hidden shadow-2xl">
              <Image
                src={showcaseImages[currentImageIndex].src}
                alt={showcaseImages[currentImageIndex].title}
                fill
                className="object-cover transition-all duration-500"
              />
              
              {/* Navigation Arrows */}
              <button
                onClick={prevImage}
                className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-all duration-300"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              
              <button
                onClick={nextImage}
                className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-all duration-300"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>

              {/* Image Info Overlay */}
              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6">
                <h3 className="text-xl font-bold text-white mb-2">
                  {showcaseImages[currentImageIndex].title}
                </h3>
                <p className="text-gray-200">
                  {showcaseImages[currentImageIndex].description}
                </p>
              </div>
            </div>

            {/* Dots Indicator */}
            <div className="flex justify-center mt-6 space-x-2">
              {showcaseImages.map((_, index) => (
                <button
                  key={index}
                  onClick={() => goToImage(index)}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    index === currentImageIndex
                      ? 'bg-accent scale-125'
                      : 'bg-gray-300 dark:bg-gray-600 hover:bg-accent/50'
                  }`}
                />
              ))}
            </div>
          </div>

          {/* Content */}
          <div className={`space-y-6 ${isVisible ? 'animate-fade-in-up' : 'opacity-0'}`} style={{ animationDelay: '0.3s' }}>
            <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl p-6 shadow-lg">
              <h3 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">
                რატომ ჩვენი გასვლები?
              </h3>
              <ul className="space-y-3 text-gray-600 dark:text-gray-300">
                <li className="flex items-start gap-3">
                  <span className="text-accent text-xl">🌲</span>
                  <span>ბუნებრივ გარემოში უნიკალური გამოცდილება</span>
                </li>
                <li className="flex items-start gap-3">
                  <span className="text-accent text-xl">👥</span>
                  <span>მეგობრული და უსაფრთხო ატმოსფერო</span>
                </li>
                <li className="flex items-start gap-3">
                  <span className="text-accent text-xl">🔥</span>
                  <span>კოცონის შუქზე საშიში ისტორიები</span>
                </li>
                <li className="flex items-start gap-3">
                  <span className="text-accent text-xl">📸</span>
                  <span>დაუვიწყარი მოგონებები და ფოტოები</span>
                </li>
              </ul>
            </div>

            <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl p-6 shadow-lg">
              <h4 className="text-xl font-semibold text-gray-800 dark:text-white mb-3">
                რას ვთავაზობთ:
              </h4>
              <div className="grid grid-cols-2 gap-4 text-sm text-gray-600 dark:text-gray-300">
                <div className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-primary rounded-full"></span>
                  <span>ტრანსპორტი</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-primary rounded-full"></span>
                  <span>კვება</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-primary rounded-full"></span>
                  <span>ღია ცეცხლი</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-primary rounded-full"></span>
                  <span>ფოტო/ვიდეო</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-primary rounded-full"></span>
                  <span>გიდი</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="w-2 h-2 bg-primary rounded-full"></span>
                  <span>უსაფრთხოება</span>
                </div>
              </div>
            </div>

            <div className="text-center">
              <button 
                onClick={() => {
                  document.querySelector('#trips-section')?.scrollIntoView({ 
                    behavior: 'smooth' 
                  });
                }}
                className="bg-accent hover:bg-accent-light text-white px-8 py-3 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg"
              >
                გასვლების ნახვა
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
