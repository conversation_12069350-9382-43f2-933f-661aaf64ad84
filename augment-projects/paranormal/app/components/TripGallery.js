'use client';

import { useState } from 'react';
import Image from 'next/image';

export default function TripGallery({ isOpen, onClose, trip }) {
  const [selectedMedia, setSelectedMedia] = useState(null);
  const [currentIndex, setCurrentIndex] = useState(0);

  // Mock gallery data - ეს მერე API-დან მოვა
  const galleryItems = [
    {
      id: 1,
      type: 'image',
      src: '/images/picnic.jpg',
      caption: 'ბუნებაში პიკნიკი და მზის ჩასვლა'
    },
    {
      id: 2,
      type: 'image',
      src: '/images/activities.jpg',
      caption: 'ჯგუფური აქტივობები და თამაშები'
    },
    {
      id: 3,
      type: 'image',
      src: '/images/paranormal.jpg',
      caption: 'კოცონის შუქზე საშიში ისტორიების მოყოლა'
    },
    {
      id: 4,
      type: 'video',
      src: '/videos/campfire-stories.mp4', // Mock video
      thumbnail: '/images/scary-histories.jpg',
      caption: 'კოცონთან საშიში ისტორიები - ვიდეო'
    }
  ];

  const openLightbox = (item, index) => {
    setSelectedMedia(item);
    setCurrentIndex(index);
  };

  const closeLightbox = () => {
    setSelectedMedia(null);
  };

  const nextMedia = () => {
    const nextIndex = (currentIndex + 1) % galleryItems.length;
    setCurrentIndex(nextIndex);
    setSelectedMedia(galleryItems[nextIndex]);
  };

  const prevMedia = () => {
    const prevIndex = currentIndex === 0 ? galleryItems.length - 1 : currentIndex - 1;
    setCurrentIndex(prevIndex);
    setSelectedMedia(galleryItems[prevIndex]);
  };

  if (!isOpen || !trip) return null;

  return (
    <>
      {/* Gallery Modal */}
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
          {/* Header */}
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold text-gray-800 dark:text-white">
                  {trip.title}
                </h2>
                <p className="text-gray-600 dark:text-gray-300 mt-1">
                  ფოტო და ვიდეო მასალა • {trip.date}
                </p>
              </div>
              <button
                onClick={onClose}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>

          {/* Trip Summary */}
          <div className="p-6 bg-gray-50 dark:bg-gray-700">
            <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-3">
              გასვლის შესახებ
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              {trip.description}
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="flex items-center gap-2">
                <span className="text-gray-500">📅</span>
                <span className="text-gray-700 dark:text-gray-300">თარიღი: {trip.date}</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-gray-500">📍</span>
                <span className="text-gray-700 dark:text-gray-300">ადგილი: {trip.location}</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-gray-500">👥</span>
                <span className="text-gray-700 dark:text-gray-300">მონაწილეები: {trip.currentParticipants}</span>
              </div>
            </div>
          </div>

          {/* Gallery Grid */}
          <div className="p-6">
            <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
              ფოტო და ვიდეო მასალა
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {galleryItems.map((item, index) => (
                <div
                  key={item.id}
                  className="relative aspect-square rounded-lg overflow-hidden cursor-pointer group"
                  onClick={() => openLightbox(item, index)}
                >
                  <Image
                    src={item.type === 'video' ? item.thumbnail : item.src}
                    alt={item.caption}
                    fill
                    className="object-cover transition-transform duration-300 group-hover:scale-110"
                  />
                  
                  {/* Video Play Button */}
                  {item.type === 'video' && (
                    <div className="absolute inset-0 flex items-center justify-center bg-black/30 group-hover:bg-black/50 transition-colors">
                      <div className="w-12 h-12 bg-white/90 rounded-full flex items-center justify-center">
                        <svg className="w-6 h-6 text-gray-800 ml-1" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M8 5v14l11-7z"/>
                        </svg>
                      </div>
                    </div>
                  )}

                  {/* Hover Overlay */}
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors"></div>
                </div>
              ))}
            </div>

            {/* Trip Story */}
            <div className="mt-8 p-6 bg-gradient-to-r from-mystery/10 to-accent/10 rounded-lg">
              <h4 className="text-lg font-semibold text-gray-800 dark:text-white mb-3">
                გასვლის ისტორია
              </h4>
              <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                ეს იყო განსაკუთრებული გასვლა {trip.location}-ში, სადაც ჩვენმა ჯგუფმა გაატარა 
                დაუვიწყარი დრო ბუნების ლოყაში. დღის განმავლობაში ვისიამოვნეთ ბუნებრივი სილამაზით, 
                ვითამაშეთ ჯგუფური თამაშები და ვიზიარეთ საინტერესო ისტორიები. 
                მზის ჩასვლისას კი ვანთეთ კოცონი და მოვისმინეთ საშიში ლეგენდები, 
                რაც განსაკუთრებულ ატმოსფეროს შექმნა. ყველა მონაწილე დარჩა კმაყოფილი 
                და ელოდება შემდეგ გასვლას.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Lightbox */}
      {selectedMedia && (
        <div className="fixed inset-0 z-60 flex items-center justify-center bg-black/90 backdrop-blur-sm">
          <div className="relative max-w-4xl max-h-[90vh] w-full h-full flex items-center justify-center p-4">
            {/* Close Button */}
            <button
              onClick={closeLightbox}
              className="absolute top-4 right-4 z-10 text-white hover:text-gray-300 transition-colors"
            >
              <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            {/* Navigation Buttons */}
            <button
              onClick={prevMedia}
              className="absolute left-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 transition-colors"
            >
              <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>

            <button
              onClick={nextMedia}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 transition-colors"
            >
              <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>

            {/* Media Content */}
            <div className="relative w-full h-full flex items-center justify-center">
              {selectedMedia.type === 'image' ? (
                <div className="relative max-w-full max-h-full">
                  <Image
                    src={selectedMedia.src}
                    alt={selectedMedia.caption}
                    width={800}
                    height={600}
                    className="max-w-full max-h-full object-contain"
                  />
                </div>
              ) : (
                <video
                  src={selectedMedia.src}
                  controls
                  className="max-w-full max-h-full"
                  autoPlay
                >
                  თქვენი ბრაუზერი არ უჭერს მხარს ვიდეო ელემენტს.
                </video>
              )}
              
              {/* Caption */}
              <div className="absolute bottom-4 left-4 right-4 bg-black/70 text-white p-3 rounded-lg">
                <p className="text-center">{selectedMedia.caption}</p>
                <p className="text-sm text-gray-300 text-center mt-1">
                  {currentIndex + 1} / {galleryItems.length}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
