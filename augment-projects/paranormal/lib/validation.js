import <PERSON>i from 'joi';

// Phone number validation for Georgian numbers
const georgianPhoneRegex = /^(\+995|995|0)?[57]\d{8}$/;

// Custom phone validation
const phoneValidation = Joi.string()
  .pattern(georgianPhoneRegex)
  .required()
  .messages({
    'string.pattern.base': 'Phone number must be a valid Georgian mobile number',
    'any.required': 'Phone number is required'
  });

// Registration validation schema
export const registerSchema = Joi.object({
  firstName: Joi.string()
    .min(2)
    .max(50)
    .required()
    .messages({
      'string.min': 'First name must be at least 2 characters',
      'string.max': 'First name cannot exceed 50 characters',
      'any.required': 'First name is required'
    }),
    
  lastName: Joi.string()
    .min(2)
    .max(50)
    .required()
    .messages({
      'string.min': 'Last name must be at least 2 characters',
      'string.max': 'Last name cannot exceed 50 characters',
      'any.required': 'Last name is required'
    }),
    
  email: Joi.string()
    .email()
    .allow(null, '')
    .messages({
      'string.email': 'Please provide a valid email address'
    }),
    
  phone: phoneValidation,
  
  facebookUrl: Joi.string()
    .uri()
    .allow(null, '')
    .messages({
      'string.uri': 'Please provide a valid Facebook URL'
    }),
    
  gender: Joi.string()
    .valid('male', 'female', 'other')
    .required()
    .messages({
      'any.only': 'Gender must be male, female, or other',
      'any.required': 'Gender is required'
    }),
    
  age: Joi.number()
    .integer()
    .min(16)
    .max(100)
    .allow(null)
    .messages({
      'number.min': 'Age must be at least 16',
      'number.max': 'Age cannot exceed 100'
    }),
    
  password: Joi.string()
    .min(8)
    .max(128)
    .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .required()
    .messages({
      'string.min': 'Password must be at least 8 characters',
      'string.max': 'Password cannot exceed 128 characters',
      'string.pattern.base': 'Password must contain at least one lowercase letter, one uppercase letter, and one number',
      'any.required': 'Password is required'
    }),
    
  confirmPassword: Joi.string()
    .valid(Joi.ref('password'))
    .required()
    .messages({
      'any.only': 'Passwords do not match',
      'any.required': 'Password confirmation is required'
    })
});

// Login validation schema
export const loginSchema = Joi.object({
  email: Joi.string()
    .email()
    .required()
    .messages({
      'string.email': 'Please provide a valid email address',
      'any.required': 'Email is required'
    }),
    
  password: Joi.string()
    .required()
    .messages({
      'any.required': 'Password is required'
    })
});

// Phone verification schema
export const phoneVerificationSchema = Joi.object({
  code: Joi.string()
    .length(6)
    .pattern(/^\d{6}$/)
    .required()
    .messages({
      'string.length': 'Verification code must be 6 digits',
      'string.pattern.base': 'Verification code must contain only numbers',
      'any.required': 'Verification code is required'
    })
});

// Validate request data
export function validateData(schema, data) {
  const { error, value } = schema.validate(data, {
    abortEarly: false,
    stripUnknown: true
  });
  
  if (error) {
    const errors = {};
    error.details.forEach(detail => {
      errors[detail.path[0]] = detail.message;
    });
    return { isValid: false, errors, data: null };
  }
  
  return { isValid: true, errors: null, data: value };
}

// Sanitize user data for response
export function sanitizeUser(user) {
  if (!user) return null;
  
  const {
    password_hash,
    phone_verification_code,
    phone_verification_expires_at,
    ...sanitizedUser
  } = user;
  
  return {
    id: sanitizedUser.id,
    firstName: sanitizedUser.first_name,
    lastName: sanitizedUser.last_name,
    name: `${sanitizedUser.first_name} ${sanitizedUser.last_name}`,
    email: sanitizedUser.email,
    phone: sanitizedUser.phone,
    facebookUrl: sanitizedUser.facebook_url,
    gender: sanitizedUser.gender,
    age: sanitizedUser.age,
    isVerified: sanitizedUser.is_verified,
    createdAt: sanitizedUser.created_at,
    updatedAt: sanitizedUser.updated_at
  };
}
