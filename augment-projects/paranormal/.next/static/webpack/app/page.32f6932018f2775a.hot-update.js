"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/components/HeroSection.js":
/*!***************************************!*\
  !*** ./app/components/HeroSection.js ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HeroSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction HeroSection() {\n    _s();\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"HeroSection.useEffect\": ()=>{\n            setIsVisible(true);\n        }\n    }[\"HeroSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"hero\",\n        className: \"relative min-h-screen flex items-center justify-center overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 z-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        src: \"/images/scary-histories.jpg\",\n                        alt: \"საშიში ისტორიები ბუნებაში\",\n                        fill: true,\n                        className: \"object-cover\",\n                        priority: true\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/HeroSection.js\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black/50 dark:bg-black/70\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/HeroSection.js\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/HeroSection.js\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 text-center px-4 max-w-4xl mx-auto \".concat(isVisible ? 'animate-fade-in-up' : 'opacity-0'),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight\",\n                        children: [\n                            \"ბუნებაში გასვლა\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"block text-2xl md:text-3xl lg:text-4xl text-accent-light mt-2\",\n                                children: \"პარანორმალური მოვლენების მკვლევარი\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/HeroSection.js\",\n                                lineNumber: 32,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/HeroSection.js\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg md:text-xl text-gray-200 mb-8 max-w-2xl mx-auto leading-relaxed\",\n                        children: \"შემოუერთდით ჩვენს ბუნებაში გასვლებს, სადაც ერთიანდება ბუნებრივი სილამაზე, ადამიანური ურთიერთობები და საშიში ისტორიების მოყოლა კოცონის შუქზე\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/HeroSection.js\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4 justify-center items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    var _document_querySelector;\n                                    (_document_querySelector = document.querySelector('#trips-section')) === null || _document_querySelector === void 0 ? void 0 : _document_querySelector.scrollIntoView({\n                                        behavior: 'smooth'\n                                    });\n                                },\n                                className: \"bg-primary hover:bg-primary-dark text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 transform hover:scale-105 animate-pulse-glow\",\n                                children: \"გასვლების ნახვა\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/HeroSection.js\",\n                                lineNumber: 43,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"border-2 border-accent text-accent hover:bg-accent hover:text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 transform hover:scale-105\",\n                                children: \"ჩვენს შესახებ\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/HeroSection.js\",\n                                lineNumber: 54,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/HeroSection.js\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-6 h-10 border-2 border-white rounded-full flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-1 h-3 bg-white rounded-full mt-2 animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/HeroSection.js\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/HeroSection.js\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/HeroSection.js\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/HeroSection.js\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-20 left-10 w-20 h-20 border border-accent/30 rounded-full animate-pulse hidden lg:block\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/HeroSection.js\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-20 right-10 w-16 h-16 border border-mystery/30 rounded-full animate-pulse hidden lg:block\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/HeroSection.js\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/HeroSection.js\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n_s(HeroSection, \"J3yJOyGdBT4L7hs1p1XQYVGMdrY=\");\n_c = HeroSection;\nvar _c;\n$RefreshReg$(_c, \"HeroSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/HeroSection.js\n"));

/***/ })

});