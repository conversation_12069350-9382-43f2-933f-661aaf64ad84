/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/components/AuthModal.js":
/*!*************************************!*\
  !*** ./app/components/AuthModal.js ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../context/AuthContext */ \"(app-pages-browser)/./app/context/AuthContext.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction AuthModal(param) {\n    let { isOpen, onClose, type, onSwitchType } = param;\n    _s();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [verificationCode, setVerificationCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isVerificationSent, setIsVerificationSent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { login, register, verifyPhone } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    // Login form data\n    const [loginData, setLoginData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        email: '',\n        password: ''\n    });\n    // Registration form data\n    const [registerData, setRegisterData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        firstName: '',\n        lastName: '',\n        phone: '',\n        facebookUrl: '',\n        gender: '',\n        password: '',\n        confirmPassword: '',\n        age: '',\n        termsAccepted: false\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthModal.useEffect\": ()=>{\n            if (isOpen) {\n                setCurrentStep(1);\n                setErrors({});\n                setVerificationCode('');\n                setIsVerificationSent(false);\n                setIsLoading(false);\n            }\n        }\n    }[\"AuthModal.useEffect\"], [\n        isOpen,\n        type\n    ]);\n    const validateEmail = (email)=>{\n        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n        return emailRegex.test(email);\n    };\n    const validatePhone = (phone)=>{\n        const phoneRegex = /^(\\+995|995)?[0-9]{9}$/;\n        return phoneRegex.test(phone.replace(/\\s/g, ''));\n    };\n    const validatePassword = (password)=>{\n        return password.length >= 8 && /[A-Z]/.test(password) && /[a-z]/.test(password) && /[0-9]/.test(password);\n    };\n    const validateFacebookUrl = (url)=>{\n        if (!url) return true; // Optional field\n        const facebookRegex = /^https?:\\/\\/(www\\.)?facebook\\.com\\/[a-zA-Z0-9.]+$/;\n        return facebookRegex.test(url);\n    };\n    const handleLoginSubmit = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        setErrors({});\n        const newErrors = {};\n        if (!validateEmail(loginData.email)) {\n            newErrors.email = 'გთხოვთ შეიყვანოთ სწორი ელ-ფოსტა';\n        }\n        if (!loginData.password) {\n            newErrors.password = 'პაროლი აუცილებელია';\n        }\n        if (Object.keys(newErrors).length > 0) {\n            setErrors(newErrors);\n            setIsLoading(false);\n            return;\n        }\n        const result = await login(loginData.email, loginData.password);\n        setIsLoading(false);\n        if (result.success) {\n            onClose();\n        } else {\n            setErrors({\n                general: 'არასწორი ელ-ფოსტა ან პაროლი'\n            });\n        }\n    };\n    const handleRegisterStep1 = (e)=>{\n        e.preventDefault();\n        const newErrors = {};\n        if (!registerData.firstName.trim()) {\n            newErrors.firstName = 'სახელი აუცილებელია';\n        }\n        if (!registerData.lastName.trim()) {\n            newErrors.lastName = 'გვარი აუცილებელია';\n        }\n        if (!validatePhone(registerData.phone)) {\n            newErrors.phone = 'გთხოვთ შეიყვანოთ სწორი ტელეფონის ნომერი';\n        }\n        if (!validateFacebookUrl(registerData.facebookUrl)) {\n            newErrors.facebookUrl = 'გთხოვთ შეიყვანოთ სწორი Facebook URL';\n        }\n        if (!registerData.gender) {\n            newErrors.gender = 'სქესის მითითება აუცილებელია';\n        }\n        if (Object.keys(newErrors).length > 0) {\n            setErrors(newErrors);\n            return;\n        }\n        setErrors({});\n        setCurrentStep(2);\n    };\n    const handleRegisterStep2 = async (e)=>{\n        e.preventDefault();\n        const newErrors = {};\n        if (!validatePassword(registerData.password)) {\n            newErrors.password = 'პაროლი უნდა შეიცავდეს მინიმუმ 8 სიმბოლოს, დიდ და პატარა ასოებს და ციფრს';\n        }\n        if (registerData.password !== registerData.confirmPassword) {\n            newErrors.confirmPassword = 'პაროლები არ ემთხვევა';\n        }\n        if (!registerData.termsAccepted) {\n            newErrors.termsAccepted = 'წესებისა და პირობების მიღება აუცილებელია';\n        }\n        if (Object.keys(newErrors).length > 0) {\n            setErrors(newErrors);\n            return;\n        }\n        setIsLoading(true);\n        setErrors({});\n        // Simulate sending verification code\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        setIsVerificationSent(true);\n        setIsLoading(false);\n        setCurrentStep(3);\n    };\n    const handlePhoneVerification = async (e)=>{\n        e.preventDefault();\n        if (verificationCode.length !== 6) {\n            setErrors({\n                verification: 'გთხოვთ შეიყვანოთ 6-ნიშნა კოდი'\n            });\n            return;\n        }\n        setIsLoading(true);\n        setErrors({});\n        const verifyResult = await verifyPhone(verificationCode);\n        if (verifyResult.success) {\n            // Complete registration\n            const userData = {\n                name: \"\".concat(registerData.firstName, \" \").concat(registerData.lastName),\n                email: \"\".concat(registerData.firstName.toLowerCase(), \".\").concat(registerData.lastName.toLowerCase(), \"@example.com\"),\n                phone: registerData.phone,\n                facebookUrl: registerData.facebookUrl,\n                gender: registerData.gender,\n                age: registerData.age || null,\n                password: registerData.password\n            };\n            const result = await register(userData);\n            setIsLoading(false);\n            if (result.success) {\n                setCurrentStep(4); // Success step\n                setTimeout(()=>{\n                    onClose();\n                }, 2000);\n            } else {\n                setErrors({\n                    general: 'რეგისტრაცია ვერ დასრულდა'\n                });\n            }\n        } else {\n            setIsLoading(false);\n            setErrors({\n                verification: 'არასწორი კოდი'\n            });\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 modal-backdrop\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white dark:bg-gray-800 rounded-xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-y-auto animate-scale-in\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-gray-800 dark:text-white\",\n                            children: type === 'login' ? 'შესვლა' : 'რეგისტრაცია'\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                            lineNumber: 201,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-6 h-6\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M6 18L18 6M6 6l12 12\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                    lineNumber: 209,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                lineNumber: 208,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                    lineNumber: 200,\n                    columnNumber: 9\n                }, this),\n                type === 'register' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-6 pt-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        \"ნაბიჯი \",\n                                        currentStep,\n                                        \" / 4\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                    lineNumber: 218,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        Math.round(currentStep / 4 * 100),\n                                        \"%\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                    lineNumber: 219,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                            lineNumber: 217,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-primary h-2 rounded-full progress-bar\",\n                                style: {\n                                    width: \"\".concat(currentStep / 4 * 100, \"%\")\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                lineNumber: 222,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                            lineNumber: 221,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                    lineNumber: 216,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6\",\n                    children: [\n                        errors.general && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4 p-3 bg-red-100 dark:bg-red-900/20 border border-red-300 dark:border-red-700 rounded-lg text-red-700 dark:text-red-400 text-sm\",\n                            children: errors.general\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                            lineNumber: 233,\n                            columnNumber: 13\n                        }, this),\n                        type === 'login' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleLoginSubmit,\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: \"ელ-ფოსტა\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                            lineNumber: 241,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            value: loginData.email,\n                                            onChange: (e)=>setLoginData({\n                                                    ...loginData,\n                                                    email: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white \".concat(errors.email ? 'input-error' : 'border-gray-300 dark:border-gray-600'),\n                                            placeholder: \"<EMAIL>\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                            lineNumber: 244,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-600 dark:text-red-400\",\n                                            children: errors.email\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                            lineNumber: 253,\n                                            columnNumber: 34\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                    lineNumber: 240,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: \"პაროლი\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                            lineNumber: 257,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"password\",\n                                            value: loginData.password,\n                                            onChange: (e)=>setLoginData({\n                                                    ...loginData,\n                                                    password: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white \".concat(errors.password ? 'input-error' : 'border-gray-300 dark:border-gray-600'),\n                                            placeholder: \"••••••••\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                            lineNumber: 260,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-red-600 dark:text-red-400\",\n                                            children: errors.password\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                            lineNumber: 269,\n                                            columnNumber: 37\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                    lineNumber: 256,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: isLoading,\n                                    className: \"w-full bg-primary hover:bg-primary-dark text-white py-2 px-4 rounded-lg font-medium transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: isLoading ? 'შესვლა...' : 'შესვლა'\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                    lineNumber: 272,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>onSwitchType('register'),\n                                        className: \"text-primary hover:text-primary-dark text-sm transition-colors\",\n                                        children: \"არ გაქვთ ანგარიში? დარეგისტრირდით\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                        lineNumber: 281,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                    lineNumber: 280,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                            lineNumber: 239,\n                            columnNumber: 13\n                        }, this) : // Registration forms based on current step\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                currentStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleRegisterStep1,\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-800 dark:text-white mb-4\",\n                                            children: \"პირადი ინფორმაცია\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                            lineNumber: 295,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                                            children: \"სახელი *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: registerData.firstName,\n                                                            onChange: (e)=>setRegisterData({\n                                                                    ...registerData,\n                                                                    firstName: e.target.value\n                                                                }),\n                                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white \".concat(errors.firstName ? 'input-error' : 'border-gray-300 dark:border-gray-600')\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                            lineNumber: 304,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        errors.firstName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"mt-1 text-sm text-red-600 dark:text-red-400\",\n                                                            children: errors.firstName\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 44\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                                            children: \"გვარი *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: registerData.lastName,\n                                                            onChange: (e)=>setRegisterData({\n                                                                    ...registerData,\n                                                                    lastName: e.target.value\n                                                                }),\n                                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white \".concat(errors.lastName ? 'input-error' : 'border-gray-300 dark:border-gray-600')\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                            lineNumber: 319,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        errors.lastName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"mt-1 text-sm text-red-600 dark:text-red-400\",\n                                                            children: errors.lastName\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                            lineNumber: 327,\n                                                            columnNumber: 43\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                            lineNumber: 299,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                                    children: \"ტელეფონის ნომერი *\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"tel\",\n                                                    value: registerData.phone,\n                                                    onChange: (e)=>setRegisterData({\n                                                            ...registerData,\n                                                            phone: e.target.value\n                                                        }),\n                                                    className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white \".concat(errors.phone ? 'input-error' : 'border-gray-300 dark:border-gray-600'),\n                                                    placeholder: \"+995 555 123 456\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 21\n                                                }, this),\n                                                errors.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-1 text-sm text-red-600 dark:text-red-400\",\n                                                    children: errors.phone\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 38\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                            lineNumber: 331,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                                    children: \"Facebook პროფილი *\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"url\",\n                                                    value: registerData.facebookUrl,\n                                                    onChange: (e)=>setRegisterData({\n                                                            ...registerData,\n                                                            facebookUrl: e.target.value\n                                                        }),\n                                                    className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white \".concat(errors.facebookUrl ? 'input-error' : 'border-gray-300 dark:border-gray-600'),\n                                                    placeholder: \"https://facebook.com/yourprofile\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                    lineNumber: 351,\n                                                    columnNumber: 21\n                                                }, this),\n                                                errors.facebookUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-1 text-sm text-red-600 dark:text-red-400\",\n                                                    children: errors.facebookUrl\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 44\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                            lineNumber: 347,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                                            children: \"სქესი *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: registerData.gender,\n                                                            onChange: (e)=>setRegisterData({\n                                                                    ...registerData,\n                                                                    gender: e.target.value\n                                                                }),\n                                                            className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white \".concat(errors.gender ? 'input-error' : 'border-gray-300 dark:border-gray-600'),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"\",\n                                                                    children: \"აირჩიეთ\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                                    lineNumber: 375,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"male\",\n                                                                    children: \"მამრობითი\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                                    lineNumber: 376,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"female\",\n                                                                    children: \"მდედრობითი\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                                    lineNumber: 377,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"other\",\n                                                                    children: \"სხვა\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                                    lineNumber: 378,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                            lineNumber: 368,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        errors.gender && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"mt-1 text-sm text-red-600 dark:text-red-400\",\n                                                            children: errors.gender\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                                            children: \"ასაკი\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                            lineNumber: 384,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            min: \"16\",\n                                                            max: \"100\",\n                                                            value: registerData.age,\n                                                            onChange: (e)=>setRegisterData({\n                                                                    ...registerData,\n                                                                    age: e.target.value\n                                                                }),\n                                                            className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                            lineNumber: 387,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                    lineNumber: 383,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                            lineNumber: 363,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            className: \"w-full bg-primary hover:bg-primary-dark text-white py-2 px-4 rounded-lg font-medium transition-all duration-300\",\n                                            children: \"შემდეგი ნაბიჯი\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                            lineNumber: 398,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>onSwitchType('login'),\n                                                className: \"text-primary hover:text-primary-dark text-sm transition-colors\",\n                                                children: \"უკვე გაქვთ ანგარიში? შედით\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                lineNumber: 406,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                            lineNumber: 405,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                    lineNumber: 294,\n                                    columnNumber: 17\n                                }, this),\n                                currentStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleRegisterStep2,\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-800 dark:text-white mb-4\",\n                                            children: \"პაროლის შექმნა\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                            lineNumber: 419,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                                    children: \"პაროლი *\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"password\",\n                                                    value: registerData.password,\n                                                    onChange: (e)=>setRegisterData({\n                                                            ...registerData,\n                                                            password: e.target.value\n                                                        }),\n                                                    className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white \".concat(errors.password ? 'input-error' : 'border-gray-300 dark:border-gray-600'),\n                                                    placeholder: \"••••••••\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                    lineNumber: 427,\n                                                    columnNumber: 21\n                                                }, this),\n                                                errors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-1 text-sm text-red-600 dark:text-red-400\",\n                                                    children: errors.password\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                    lineNumber: 436,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-1 text-xs text-gray-500 dark:text-gray-400\",\n                                                    children: \"მინიმუმ 8 სიმბოლო, დიდი და პატარა ასოები, ციფრი\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                    lineNumber: 437,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                            lineNumber: 423,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                                    children: \"პაროლის დადასტურება *\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                    lineNumber: 443,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"password\",\n                                                    value: registerData.confirmPassword,\n                                                    onChange: (e)=>setRegisterData({\n                                                            ...registerData,\n                                                            confirmPassword: e.target.value\n                                                        }),\n                                                    className: \"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white \".concat(errors.confirmPassword ? 'input-error' : 'border-gray-300 dark:border-gray-600'),\n                                                    placeholder: \"••••••••\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 21\n                                                }, this),\n                                                errors.confirmPassword && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-1 text-sm text-red-600 dark:text-red-400\",\n                                                    children: errors.confirmPassword\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 48\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                            lineNumber: 442,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"checkbox\",\n                                                    id: \"terms\",\n                                                    checked: registerData.termsAccepted,\n                                                    onChange: (e)=>setRegisterData({\n                                                            ...registerData,\n                                                            termsAccepted: e.target.checked\n                                                        }),\n                                                    className: \"mt-1 w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"terms\",\n                                                    className: \"text-sm text-gray-700 dark:text-gray-300\",\n                                                    children: [\n                                                        \"ვეთანხმები\",\n                                                        ' ',\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"#\",\n                                                            className: \"text-primary hover:text-primary-dark\",\n                                                            children: \"წესებსა და პირობებს\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                            lineNumber: 468,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                    lineNumber: 466,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                            lineNumber: 458,\n                                            columnNumber: 19\n                                        }, this),\n                                        errors.termsAccepted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-red-600 dark:text-red-400\",\n                                            children: errors.termsAccepted\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                            lineNumber: 473,\n                                            columnNumber: 44\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>setCurrentStep(1),\n                                                    className: \"flex-1 bg-gray-300 hover:bg-gray-400 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-200 py-2 px-4 rounded-lg font-medium transition-all duration-300\",\n                                                    children: \"უკან\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"submit\",\n                                                    disabled: isLoading,\n                                                    className: \"flex-1 bg-primary hover:bg-primary-dark text-white py-2 px-4 rounded-lg font-medium transition-all duration-300 disabled:opacity-50\",\n                                                    children: isLoading ? 'იგზავნება...' : 'კოდის გაგზავნა'\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                    lineNumber: 483,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                            lineNumber: 475,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                    lineNumber: 418,\n                                    columnNumber: 17\n                                }, this),\n                                currentStep === 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handlePhoneVerification,\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-800 dark:text-white mb-4\",\n                                            children: \"ტელეფონის დადასტურება\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                            lineNumber: 496,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400 mb-4\",\n                                                    children: \"დადასტურების კოდი გაიგზავნა ნომერზე:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                    lineNumber: 501,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-semibold text-gray-800 dark:text-white mb-6\",\n                                                    children: registerData.phone\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                    lineNumber: 504,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                            lineNumber: 500,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 text-center\",\n                                                    children: \"შეიყვანეთ 6-ნიშნა კოდი\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    maxLength: \"6\",\n                                                    value: verificationCode,\n                                                    onChange: (e)=>setVerificationCode(e.target.value.replace(/\\D/g, '')),\n                                                    className: \"verification-input w-full px-3 py-3 border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white \".concat(errors.verification ? 'input-error' : 'border-gray-300 dark:border-gray-600'),\n                                                    placeholder: \"123456\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                    lineNumber: 513,\n                                                    columnNumber: 21\n                                                }, this),\n                                                errors.verification && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-1 text-sm text-red-600 dark:text-red-400 text-center\",\n                                                    children: errors.verification\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                    lineNumber: 523,\n                                                    columnNumber: 45\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                            lineNumber: 509,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                className: \"text-sm text-primary hover:text-primary-dark transition-colors\",\n                                                onClick: ()=>{\n                                                    setIsVerificationSent(false);\n                                                    // Resend code logic here\n                                                    setTimeout(()=>setIsVerificationSent(true), 1000);\n                                                },\n                                                children: \"კოდის ხელახლა გაგზავნა\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                lineNumber: 527,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                            lineNumber: 526,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>setCurrentStep(2),\n                                                    className: \"flex-1 bg-gray-300 hover:bg-gray-400 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-200 py-2 px-4 rounded-lg font-medium transition-all duration-300\",\n                                                    children: \"უკან\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                    lineNumber: 541,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"submit\",\n                                                    disabled: isLoading || verificationCode.length !== 6,\n                                                    className: \"flex-1 bg-primary hover:bg-primary-dark text-white py-2 px-4 rounded-lg font-medium transition-all duration-300 disabled:opacity-50\",\n                                                    children: isLoading ? 'მოწმდება...' : 'დადასტურება'\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                    lineNumber: 548,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                            lineNumber: 540,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                    lineNumber: 495,\n                                    columnNumber: 17\n                                }, this),\n                                currentStep === 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-8 h-8 text-green-600 dark:text-green-400\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M5 13l4 4L19 7\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                    lineNumber: 563,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                                lineNumber: 562,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                            lineNumber: 561,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-gray-800 dark:text-white\",\n                                            children: \"რეგისტრაცია წარმატებით დასრულდა!\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                            lineNumber: 566,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-400\",\n                                            children: \"კეთილი იყოს თქვენი მობრძანება ჩვენს საზოგადოებაში\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                            lineNumber: 569,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                                    lineNumber: 560,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n                    lineNumber: 231,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n            lineNumber: 198,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/AuthModal.js\",\n        lineNumber: 197,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthModal, \"+Pn6gPciuNvRQZz6dLOxuu1rM20=\", false, function() {\n    return [\n        _context_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = AuthModal;\nvar _c;\n$RefreshReg$(_c, \"AuthModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9jb21wb25lbnRzL0F1dGhNb2RhbC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRTRDO0FBQ0s7QUFFbEMsU0FBU0csVUFBVSxLQUF1QztRQUF2QyxFQUFFQyxNQUFNLEVBQUVDLE9BQU8sRUFBRUMsSUFBSSxFQUFFQyxZQUFZLEVBQUUsR0FBdkM7O0lBQ2hDLE1BQU0sQ0FBQ0MsYUFBYUMsZUFBZSxHQUFHVCwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUNVLFdBQVdDLGFBQWEsR0FBR1gsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDWSxRQUFRQyxVQUFVLEdBQUdiLCtDQUFRQSxDQUFDLENBQUM7SUFDdEMsTUFBTSxDQUFDYyxrQkFBa0JDLG9CQUFvQixHQUFHZiwrQ0FBUUEsQ0FBQztJQUN6RCxNQUFNLENBQUNnQixvQkFBb0JDLHNCQUFzQixHQUFHakIsK0NBQVFBLENBQUM7SUFDN0QsTUFBTSxFQUFFa0IsS0FBSyxFQUFFQyxRQUFRLEVBQUVDLFdBQVcsRUFBRSxHQUFHbEIsNkRBQU9BO0lBRWhELGtCQUFrQjtJQUNsQixNQUFNLENBQUNtQixXQUFXQyxhQUFhLEdBQUd0QiwrQ0FBUUEsQ0FBQztRQUN6Q3VCLE9BQU87UUFDUEMsVUFBVTtJQUNaO0lBRUEseUJBQXlCO0lBQ3pCLE1BQU0sQ0FBQ0MsY0FBY0MsZ0JBQWdCLEdBQUcxQiwrQ0FBUUEsQ0FBQztRQUMvQzJCLFdBQVc7UUFDWEMsVUFBVTtRQUNWQyxPQUFPO1FBQ1BDLGFBQWE7UUFDYkMsUUFBUTtRQUNSUCxVQUFVO1FBQ1ZRLGlCQUFpQjtRQUNqQkMsS0FBSztRQUNMQyxlQUFlO0lBQ2pCO0lBRUFqQyxnREFBU0E7K0JBQUM7WUFDUixJQUFJRyxRQUFRO2dCQUNWSyxlQUFlO2dCQUNmSSxVQUFVLENBQUM7Z0JBQ1hFLG9CQUFvQjtnQkFDcEJFLHNCQUFzQjtnQkFDdEJOLGFBQWE7WUFDZjtRQUNGOzhCQUFHO1FBQUNQO1FBQVFFO0tBQUs7SUFFakIsTUFBTTZCLGdCQUFnQixDQUFDWjtRQUNyQixNQUFNYSxhQUFhO1FBQ25CLE9BQU9BLFdBQVdDLElBQUksQ0FBQ2Q7SUFDekI7SUFFQSxNQUFNZSxnQkFBZ0IsQ0FBQ1Q7UUFDckIsTUFBTVUsYUFBYTtRQUNuQixPQUFPQSxXQUFXRixJQUFJLENBQUNSLE1BQU1XLE9BQU8sQ0FBQyxPQUFPO0lBQzlDO0lBRUEsTUFBTUMsbUJBQW1CLENBQUNqQjtRQUN4QixPQUFPQSxTQUFTa0IsTUFBTSxJQUFJLEtBQ25CLFFBQVFMLElBQUksQ0FBQ2IsYUFDYixRQUFRYSxJQUFJLENBQUNiLGFBQ2IsUUFBUWEsSUFBSSxDQUFDYjtJQUN0QjtJQUVBLE1BQU1tQixzQkFBc0IsQ0FBQ0M7UUFDM0IsSUFBSSxDQUFDQSxLQUFLLE9BQU8sTUFBTSxpQkFBaUI7UUFDeEMsTUFBTUMsZ0JBQWdCO1FBQ3RCLE9BQU9BLGNBQWNSLElBQUksQ0FBQ087SUFDNUI7SUFFQSxNQUFNRSxvQkFBb0IsT0FBT0M7UUFDL0JBLEVBQUVDLGNBQWM7UUFDaEJyQyxhQUFhO1FBQ2JFLFVBQVUsQ0FBQztRQUVYLE1BQU1vQyxZQUFZLENBQUM7UUFDbkIsSUFBSSxDQUFDZCxjQUFjZCxVQUFVRSxLQUFLLEdBQUc7WUFDbkMwQixVQUFVMUIsS0FBSyxHQUFHO1FBQ3BCO1FBQ0EsSUFBSSxDQUFDRixVQUFVRyxRQUFRLEVBQUU7WUFDdkJ5QixVQUFVekIsUUFBUSxHQUFHO1FBQ3ZCO1FBRUEsSUFBSTBCLE9BQU9DLElBQUksQ0FBQ0YsV0FBV1AsTUFBTSxHQUFHLEdBQUc7WUFDckM3QixVQUFVb0M7WUFDVnRDLGFBQWE7WUFDYjtRQUNGO1FBRUEsTUFBTXlDLFNBQVMsTUFBTWxDLE1BQU1HLFVBQVVFLEtBQUssRUFBRUYsVUFBVUcsUUFBUTtRQUM5RGIsYUFBYTtRQUViLElBQUl5QyxPQUFPQyxPQUFPLEVBQUU7WUFDbEJoRDtRQUNGLE9BQU87WUFDTFEsVUFBVTtnQkFBRXlDLFNBQVM7WUFBOEI7UUFDckQ7SUFDRjtJQUVBLE1BQU1DLHNCQUFzQixDQUFDUjtRQUMzQkEsRUFBRUMsY0FBYztRQUNoQixNQUFNQyxZQUFZLENBQUM7UUFFbkIsSUFBSSxDQUFDeEIsYUFBYUUsU0FBUyxDQUFDNkIsSUFBSSxJQUFJO1lBQ2xDUCxVQUFVdEIsU0FBUyxHQUFHO1FBQ3hCO1FBQ0EsSUFBSSxDQUFDRixhQUFhRyxRQUFRLENBQUM0QixJQUFJLElBQUk7WUFDakNQLFVBQVVyQixRQUFRLEdBQUc7UUFDdkI7UUFDQSxJQUFJLENBQUNVLGNBQWNiLGFBQWFJLEtBQUssR0FBRztZQUN0Q29CLFVBQVVwQixLQUFLLEdBQUc7UUFDcEI7UUFDQSxJQUFJLENBQUNjLG9CQUFvQmxCLGFBQWFLLFdBQVcsR0FBRztZQUNsRG1CLFVBQVVuQixXQUFXLEdBQUc7UUFDMUI7UUFDQSxJQUFJLENBQUNMLGFBQWFNLE1BQU0sRUFBRTtZQUN4QmtCLFVBQVVsQixNQUFNLEdBQUc7UUFDckI7UUFFQSxJQUFJbUIsT0FBT0MsSUFBSSxDQUFDRixXQUFXUCxNQUFNLEdBQUcsR0FBRztZQUNyQzdCLFVBQVVvQztZQUNWO1FBQ0Y7UUFFQXBDLFVBQVUsQ0FBQztRQUNYSixlQUFlO0lBQ2pCO0lBRUEsTUFBTWdELHNCQUFzQixPQUFPVjtRQUNqQ0EsRUFBRUMsY0FBYztRQUNoQixNQUFNQyxZQUFZLENBQUM7UUFFbkIsSUFBSSxDQUFDUixpQkFBaUJoQixhQUFhRCxRQUFRLEdBQUc7WUFDNUN5QixVQUFVekIsUUFBUSxHQUFHO1FBQ3ZCO1FBQ0EsSUFBSUMsYUFBYUQsUUFBUSxLQUFLQyxhQUFhTyxlQUFlLEVBQUU7WUFDMURpQixVQUFVakIsZUFBZSxHQUFHO1FBQzlCO1FBQ0EsSUFBSSxDQUFDUCxhQUFhUyxhQUFhLEVBQUU7WUFDL0JlLFVBQVVmLGFBQWEsR0FBRztRQUM1QjtRQUVBLElBQUlnQixPQUFPQyxJQUFJLENBQUNGLFdBQVdQLE1BQU0sR0FBRyxHQUFHO1lBQ3JDN0IsVUFBVW9DO1lBQ1Y7UUFDRjtRQUVBdEMsYUFBYTtRQUNiRSxVQUFVLENBQUM7UUFFWCxxQ0FBcUM7UUFDckMsTUFBTSxJQUFJNkMsUUFBUUMsQ0FBQUEsVUFBV0MsV0FBV0QsU0FBUztRQUNqRDFDLHNCQUFzQjtRQUN0Qk4sYUFBYTtRQUNiRixlQUFlO0lBQ2pCO0lBRUEsTUFBTW9ELDBCQUEwQixPQUFPZDtRQUNyQ0EsRUFBRUMsY0FBYztRQUNoQixJQUFJbEMsaUJBQWlCNEIsTUFBTSxLQUFLLEdBQUc7WUFDakM3QixVQUFVO2dCQUFFaUQsY0FBYztZQUFnQztZQUMxRDtRQUNGO1FBRUFuRCxhQUFhO1FBQ2JFLFVBQVUsQ0FBQztRQUVYLE1BQU1rRCxlQUFlLE1BQU0zQyxZQUFZTjtRQUV2QyxJQUFJaUQsYUFBYVYsT0FBTyxFQUFFO1lBQ3hCLHdCQUF3QjtZQUN4QixNQUFNVyxXQUFXO2dCQUNmQyxNQUFNLEdBQTZCeEMsT0FBMUJBLGFBQWFFLFNBQVMsRUFBQyxLQUF5QixPQUF0QkYsYUFBYUcsUUFBUTtnQkFDeERMLE9BQU8sR0FBMkNFLE9BQXhDQSxhQUFhRSxTQUFTLENBQUN1QyxXQUFXLElBQUcsS0FBdUMsT0FBcEN6QyxhQUFhRyxRQUFRLENBQUNzQyxXQUFXLElBQUc7Z0JBQ3RGckMsT0FBT0osYUFBYUksS0FBSztnQkFDekJDLGFBQWFMLGFBQWFLLFdBQVc7Z0JBQ3JDQyxRQUFRTixhQUFhTSxNQUFNO2dCQUMzQkUsS0FBS1IsYUFBYVEsR0FBRyxJQUFJO2dCQUN6QlQsVUFBVUMsYUFBYUQsUUFBUTtZQUNqQztZQUVBLE1BQU00QixTQUFTLE1BQU1qQyxTQUFTNkM7WUFDOUJyRCxhQUFhO1lBRWIsSUFBSXlDLE9BQU9DLE9BQU8sRUFBRTtnQkFDbEI1QyxlQUFlLElBQUksZUFBZTtnQkFDbENtRCxXQUFXO29CQUNUdkQ7Z0JBQ0YsR0FBRztZQUNMLE9BQU87Z0JBQ0xRLFVBQVU7b0JBQUV5QyxTQUFTO2dCQUEyQjtZQUNsRDtRQUNGLE9BQU87WUFDTDNDLGFBQWE7WUFDYkUsVUFBVTtnQkFBRWlELGNBQWM7WUFBZ0I7UUFDNUM7SUFDRjtJQUVBLElBQUksQ0FBQzFELFFBQVEsT0FBTztJQUVwQixxQkFDRSw4REFBQytEO1FBQUlDLFdBQVU7a0JBQ2IsNEVBQUNEO1lBQUlDLFdBQVU7OzhCQUViLDhEQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNDOzRCQUFHRCxXQUFVO3NDQUNYOUQsU0FBUyxVQUFVLFdBQVc7Ozs7OztzQ0FFakMsOERBQUNnRTs0QkFDQ0MsU0FBU2xFOzRCQUNUK0QsV0FBVTtzQ0FFViw0RUFBQ0k7Z0NBQUlKLFdBQVU7Z0NBQVVLLE1BQUs7Z0NBQU9DLFFBQU87Z0NBQWVDLFNBQVE7MENBQ2pFLDRFQUFDQztvQ0FBS0MsZUFBYztvQ0FBUUMsZ0JBQWU7b0NBQVFDLGFBQWE7b0NBQUdDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0JBTTFFMUUsU0FBUyw0QkFDUiw4REFBQzZEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDYTs7d0NBQUs7d0NBQVF6RTt3Q0FBWTs7Ozs7Ozs4Q0FDMUIsOERBQUN5RTs7d0NBQU1DLEtBQUtDLEtBQUssQ0FBQyxjQUFlLElBQUs7d0NBQUs7Ozs7Ozs7Ozs7Ozs7c0NBRTdDLDhEQUFDaEI7NEJBQUlDLFdBQVU7c0NBQ2IsNEVBQUNEO2dDQUNDQyxXQUFVO2dDQUNWZ0IsT0FBTztvQ0FBRUMsT0FBTyxHQUEyQixPQUF4QixjQUFlLElBQUssS0FBSTtnQ0FBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBT3RELDhEQUFDbEI7b0JBQUlDLFdBQVU7O3dCQUNaeEQsT0FBTzBDLE9BQU8sa0JBQ2IsOERBQUNhOzRCQUFJQyxXQUFVO3NDQUNaeEQsT0FBTzBDLE9BQU87Ozs7Ozt3QkFJbEJoRCxTQUFTLHdCQUNSLDhEQUFDZ0Y7NEJBQUtDLFVBQVV6Qzs0QkFBbUJzQixXQUFVOzs4Q0FDM0MsOERBQUNEOztzREFDQyw4REFBQ3FCOzRDQUFNcEIsV0FBVTtzREFBa0U7Ozs7OztzREFHbkYsOERBQUNxQjs0Q0FDQ25GLE1BQUs7NENBQ0xvRixPQUFPckUsVUFBVUUsS0FBSzs0Q0FDdEJvRSxVQUFVLENBQUM1QyxJQUFNekIsYUFBYTtvREFBRSxHQUFHRCxTQUFTO29EQUFFRSxPQUFPd0IsRUFBRTZDLE1BQU0sQ0FBQ0YsS0FBSztnREFBQzs0Q0FDcEV0QixXQUFXLGdJQUVWLE9BREN4RCxPQUFPVyxLQUFLLEdBQUcsZ0JBQWdCOzRDQUVqQ3NFLGFBQVk7Ozs7Ozt3Q0FFYmpGLE9BQU9XLEtBQUssa0JBQUksOERBQUN1RTs0Q0FBRTFCLFdBQVU7c0RBQStDeEQsT0FBT1csS0FBSzs7Ozs7Ozs7Ozs7OzhDQUczRiw4REFBQzRDOztzREFDQyw4REFBQ3FCOzRDQUFNcEIsV0FBVTtzREFBa0U7Ozs7OztzREFHbkYsOERBQUNxQjs0Q0FDQ25GLE1BQUs7NENBQ0xvRixPQUFPckUsVUFBVUcsUUFBUTs0Q0FDekJtRSxVQUFVLENBQUM1QyxJQUFNekIsYUFBYTtvREFBRSxHQUFHRCxTQUFTO29EQUFFRyxVQUFVdUIsRUFBRTZDLE1BQU0sQ0FBQ0YsS0FBSztnREFBQzs0Q0FDdkV0QixXQUFXLGdJQUVWLE9BREN4RCxPQUFPWSxRQUFRLEdBQUcsZ0JBQWdCOzRDQUVwQ3FFLGFBQVk7Ozs7Ozt3Q0FFYmpGLE9BQU9ZLFFBQVEsa0JBQUksOERBQUNzRTs0Q0FBRTFCLFdBQVU7c0RBQStDeEQsT0FBT1ksUUFBUTs7Ozs7Ozs7Ozs7OzhDQUdqRyw4REFBQzhDO29DQUNDaEUsTUFBSztvQ0FDTHlGLFVBQVVyRjtvQ0FDVjBELFdBQVU7OENBRVQxRCxZQUFZLGNBQWM7Ozs7Ozs4Q0FHN0IsOERBQUN5RDtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ0U7d0NBQ0NoRSxNQUFLO3dDQUNMaUUsU0FBUyxJQUFNaEUsYUFBYTt3Q0FDNUI2RCxXQUFVO2tEQUNYOzs7Ozs7Ozs7Ozs7Ozs7O21DQU1MLDJDQUEyQztzQ0FDM0M7O2dDQUNHNUQsZ0JBQWdCLG1CQUNmLDhEQUFDOEU7b0NBQUtDLFVBQVVoQztvQ0FBcUJhLFdBQVU7O3NEQUM3Qyw4REFBQzRCOzRDQUFHNUIsV0FBVTtzREFBMkQ7Ozs7OztzREFJekUsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7O3NFQUNDLDhEQUFDcUI7NERBQU1wQixXQUFVO3NFQUFrRTs7Ozs7O3NFQUduRiw4REFBQ3FCOzREQUNDbkYsTUFBSzs0REFDTG9GLE9BQU9qRSxhQUFhRSxTQUFTOzREQUM3QmdFLFVBQVUsQ0FBQzVDLElBQU1yQixnQkFBZ0I7b0VBQUUsR0FBR0QsWUFBWTtvRUFBRUUsV0FBV29CLEVBQUU2QyxNQUFNLENBQUNGLEtBQUs7Z0VBQUM7NERBQzlFdEIsV0FBVyxnSUFFVixPQURDeEQsT0FBT2UsU0FBUyxHQUFHLGdCQUFnQjs7Ozs7O3dEQUd0Q2YsT0FBT2UsU0FBUyxrQkFBSSw4REFBQ21FOzREQUFFMUIsV0FBVTtzRUFBK0N4RCxPQUFPZSxTQUFTOzs7Ozs7Ozs7Ozs7OERBR25HLDhEQUFDd0M7O3NFQUNDLDhEQUFDcUI7NERBQU1wQixXQUFVO3NFQUFrRTs7Ozs7O3NFQUduRiw4REFBQ3FCOzREQUNDbkYsTUFBSzs0REFDTG9GLE9BQU9qRSxhQUFhRyxRQUFROzREQUM1QitELFVBQVUsQ0FBQzVDLElBQU1yQixnQkFBZ0I7b0VBQUUsR0FBR0QsWUFBWTtvRUFBRUcsVUFBVW1CLEVBQUU2QyxNQUFNLENBQUNGLEtBQUs7Z0VBQUM7NERBQzdFdEIsV0FBVyxnSUFFVixPQURDeEQsT0FBT2dCLFFBQVEsR0FBRyxnQkFBZ0I7Ozs7Ozt3REFHckNoQixPQUFPZ0IsUUFBUSxrQkFBSSw4REFBQ2tFOzREQUFFMUIsV0FBVTtzRUFBK0N4RCxPQUFPZ0IsUUFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQUluRyw4REFBQ3VDOzs4REFDQyw4REFBQ3FCO29EQUFNcEIsV0FBVTs4REFBa0U7Ozs7Ozs4REFHbkYsOERBQUNxQjtvREFDQ25GLE1BQUs7b0RBQ0xvRixPQUFPakUsYUFBYUksS0FBSztvREFDekI4RCxVQUFVLENBQUM1QyxJQUFNckIsZ0JBQWdCOzREQUFFLEdBQUdELFlBQVk7NERBQUVJLE9BQU9rQixFQUFFNkMsTUFBTSxDQUFDRixLQUFLO3dEQUFDO29EQUMxRXRCLFdBQVcsZ0lBRVYsT0FEQ3hELE9BQU9pQixLQUFLLEdBQUcsZ0JBQWdCO29EQUVqQ2dFLGFBQVk7Ozs7OztnREFFYmpGLE9BQU9pQixLQUFLLGtCQUFJLDhEQUFDaUU7b0RBQUUxQixXQUFVOzhEQUErQ3hELE9BQU9pQixLQUFLOzs7Ozs7Ozs7Ozs7c0RBRzNGLDhEQUFDc0M7OzhEQUNDLDhEQUFDcUI7b0RBQU1wQixXQUFVOzhEQUFrRTs7Ozs7OzhEQUduRiw4REFBQ3FCO29EQUNDbkYsTUFBSztvREFDTG9GLE9BQU9qRSxhQUFhSyxXQUFXO29EQUMvQjZELFVBQVUsQ0FBQzVDLElBQU1yQixnQkFBZ0I7NERBQUUsR0FBR0QsWUFBWTs0REFBRUssYUFBYWlCLEVBQUU2QyxNQUFNLENBQUNGLEtBQUs7d0RBQUM7b0RBQ2hGdEIsV0FBVyxnSUFFVixPQURDeEQsT0FBT2tCLFdBQVcsR0FBRyxnQkFBZ0I7b0RBRXZDK0QsYUFBWTs7Ozs7O2dEQUViakYsT0FBT2tCLFdBQVcsa0JBQUksOERBQUNnRTtvREFBRTFCLFdBQVU7OERBQStDeEQsT0FBT2tCLFdBQVc7Ozs7Ozs7Ozs7OztzREFHdkcsOERBQUNxQzs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEOztzRUFDQyw4REFBQ3FCOzREQUFNcEIsV0FBVTtzRUFBa0U7Ozs7OztzRUFHbkYsOERBQUM2Qjs0REFDQ1AsT0FBT2pFLGFBQWFNLE1BQU07NERBQzFCNEQsVUFBVSxDQUFDNUMsSUFBTXJCLGdCQUFnQjtvRUFBRSxHQUFHRCxZQUFZO29FQUFFTSxRQUFRZ0IsRUFBRTZDLE1BQU0sQ0FBQ0YsS0FBSztnRUFBQzs0REFDM0V0QixXQUFXLGdJQUVWLE9BREN4RCxPQUFPbUIsTUFBTSxHQUFHLGdCQUFnQjs7OEVBR2xDLDhEQUFDbUU7b0VBQU9SLE9BQU07OEVBQUc7Ozs7Ozs4RUFDakIsOERBQUNRO29FQUFPUixPQUFNOzhFQUFPOzs7Ozs7OEVBQ3JCLDhEQUFDUTtvRUFBT1IsT0FBTTs4RUFBUzs7Ozs7OzhFQUN2Qiw4REFBQ1E7b0VBQU9SLE9BQU07OEVBQVE7Ozs7Ozs7Ozs7Ozt3REFFdkI5RSxPQUFPbUIsTUFBTSxrQkFBSSw4REFBQytEOzREQUFFMUIsV0FBVTtzRUFBK0N4RCxPQUFPbUIsTUFBTTs7Ozs7Ozs7Ozs7OzhEQUc3Riw4REFBQ29DOztzRUFDQyw4REFBQ3FCOzREQUFNcEIsV0FBVTtzRUFBa0U7Ozs7OztzRUFHbkYsOERBQUNxQjs0REFDQ25GLE1BQUs7NERBQ0w2RixLQUFJOzREQUNKQyxLQUFJOzREQUNKVixPQUFPakUsYUFBYVEsR0FBRzs0REFDdkIwRCxVQUFVLENBQUM1QyxJQUFNckIsZ0JBQWdCO29FQUFFLEdBQUdELFlBQVk7b0VBQUVRLEtBQUtjLEVBQUU2QyxNQUFNLENBQUNGLEtBQUs7Z0VBQUM7NERBQ3hFdEIsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQUtoQiw4REFBQ0U7NENBQ0NoRSxNQUFLOzRDQUNMOEQsV0FBVTtzREFDWDs7Ozs7O3NEQUlELDhEQUFDRDs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ0U7Z0RBQ0NoRSxNQUFLO2dEQUNMaUUsU0FBUyxJQUFNaEUsYUFBYTtnREFDNUI2RCxXQUFVOzBEQUNYOzs7Ozs7Ozs7Ozs7Ozs7OztnQ0FPTjVELGdCQUFnQixtQkFDZiw4REFBQzhFO29DQUFLQyxVQUFVOUI7b0NBQXFCVyxXQUFVOztzREFDN0MsOERBQUM0Qjs0Q0FBRzVCLFdBQVU7c0RBQTJEOzs7Ozs7c0RBSXpFLDhEQUFDRDs7OERBQ0MsOERBQUNxQjtvREFBTXBCLFdBQVU7OERBQWtFOzs7Ozs7OERBR25GLDhEQUFDcUI7b0RBQ0NuRixNQUFLO29EQUNMb0YsT0FBT2pFLGFBQWFELFFBQVE7b0RBQzVCbUUsVUFBVSxDQUFDNUMsSUFBTXJCLGdCQUFnQjs0REFBRSxHQUFHRCxZQUFZOzREQUFFRCxVQUFVdUIsRUFBRTZDLE1BQU0sQ0FBQ0YsS0FBSzt3REFBQztvREFDN0V0QixXQUFXLGdJQUVWLE9BREN4RCxPQUFPWSxRQUFRLEdBQUcsZ0JBQWdCO29EQUVwQ3FFLGFBQVk7Ozs7OztnREFFYmpGLE9BQU9ZLFFBQVEsa0JBQUksOERBQUNzRTtvREFBRTFCLFdBQVU7OERBQStDeEQsT0FBT1ksUUFBUTs7Ozs7OzhEQUMvRiw4REFBQ3NFO29EQUFFMUIsV0FBVTs4REFBZ0Q7Ozs7Ozs7Ozs7OztzREFLL0QsOERBQUNEOzs4REFDQyw4REFBQ3FCO29EQUFNcEIsV0FBVTs4REFBa0U7Ozs7Ozs4REFHbkYsOERBQUNxQjtvREFDQ25GLE1BQUs7b0RBQ0xvRixPQUFPakUsYUFBYU8sZUFBZTtvREFDbkMyRCxVQUFVLENBQUM1QyxJQUFNckIsZ0JBQWdCOzREQUFFLEdBQUdELFlBQVk7NERBQUVPLGlCQUFpQmUsRUFBRTZDLE1BQU0sQ0FBQ0YsS0FBSzt3REFBQztvREFDcEZ0QixXQUFXLGdJQUVWLE9BREN4RCxPQUFPb0IsZUFBZSxHQUFHLGdCQUFnQjtvREFFM0M2RCxhQUFZOzs7Ozs7Z0RBRWJqRixPQUFPb0IsZUFBZSxrQkFBSSw4REFBQzhEO29EQUFFMUIsV0FBVTs4REFBK0N4RCxPQUFPb0IsZUFBZTs7Ozs7Ozs7Ozs7O3NEQUcvRyw4REFBQ21DOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ3FCO29EQUNDbkYsTUFBSztvREFDTCtGLElBQUc7b0RBQ0hDLFNBQVM3RSxhQUFhUyxhQUFhO29EQUNuQ3lELFVBQVUsQ0FBQzVDLElBQU1yQixnQkFBZ0I7NERBQUUsR0FBR0QsWUFBWTs0REFBRVMsZUFBZWEsRUFBRTZDLE1BQU0sQ0FBQ1UsT0FBTzt3REFBQztvREFDcEZsQyxXQUFVOzs7Ozs7OERBRVosOERBQUNvQjtvREFBTWUsU0FBUTtvREFBUW5DLFdBQVU7O3dEQUEyQzt3REFDL0Q7c0VBQ1gsOERBQUNvQzs0REFBRUMsTUFBSzs0REFBSXJDLFdBQVU7c0VBQXVDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7d0NBS2hFeEQsT0FBT3NCLGFBQWEsa0JBQUksOERBQUM0RDs0Q0FBRTFCLFdBQVU7c0RBQTBDeEQsT0FBT3NCLGFBQWE7Ozs7OztzREFFcEcsOERBQUNpQzs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNFO29EQUNDaEUsTUFBSztvREFDTGlFLFNBQVMsSUFBTTlELGVBQWU7b0RBQzlCMkQsV0FBVTs4REFDWDs7Ozs7OzhEQUdELDhEQUFDRTtvREFDQ2hFLE1BQUs7b0RBQ0x5RixVQUFVckY7b0RBQ1YwRCxXQUFVOzhEQUVUMUQsWUFBWSxpQkFBaUI7Ozs7Ozs7Ozs7Ozs7Ozs7OztnQ0FNckNGLGdCQUFnQixtQkFDZiw4REFBQzhFO29DQUFLQyxVQUFVMUI7b0NBQXlCTyxXQUFVOztzREFDakQsOERBQUM0Qjs0Q0FBRzVCLFdBQVU7c0RBQTJEOzs7Ozs7c0RBSXpFLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUMwQjtvREFBRTFCLFdBQVU7OERBQWdEOzs7Ozs7OERBRzdELDhEQUFDMEI7b0RBQUUxQixXQUFVOzhEQUNWM0MsYUFBYUksS0FBSzs7Ozs7Ozs7Ozs7O3NEQUl2Qiw4REFBQ3NDOzs4REFDQyw4REFBQ3FCO29EQUFNcEIsV0FBVTs4REFBOEU7Ozs7Ozs4REFHL0YsOERBQUNxQjtvREFDQ25GLE1BQUs7b0RBQ0xvRyxXQUFVO29EQUNWaEIsT0FBTzVFO29EQUNQNkUsVUFBVSxDQUFDNUMsSUFBTWhDLG9CQUFvQmdDLEVBQUU2QyxNQUFNLENBQUNGLEtBQUssQ0FBQ2xELE9BQU8sQ0FBQyxPQUFPO29EQUNuRTRCLFdBQVcsbUpBRVYsT0FEQ3hELE9BQU9rRCxZQUFZLEdBQUcsZ0JBQWdCO29EQUV4QytCLGFBQVk7Ozs7OztnREFFYmpGLE9BQU9rRCxZQUFZLGtCQUFJLDhEQUFDZ0M7b0RBQUUxQixXQUFVOzhEQUEyRHhELE9BQU9rRCxZQUFZOzs7Ozs7Ozs7Ozs7c0RBR3JILDhEQUFDSzs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ0U7Z0RBQ0NoRSxNQUFLO2dEQUNMOEQsV0FBVTtnREFDVkcsU0FBUztvREFDUHRELHNCQUFzQjtvREFDdEIseUJBQXlCO29EQUN6QjJDLFdBQVcsSUFBTTNDLHNCQUFzQixPQUFPO2dEQUNoRDswREFDRDs7Ozs7Ozs7Ozs7c0RBS0gsOERBQUNrRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNFO29EQUNDaEUsTUFBSztvREFDTGlFLFNBQVMsSUFBTTlELGVBQWU7b0RBQzlCMkQsV0FBVTs4REFDWDs7Ozs7OzhEQUdELDhEQUFDRTtvREFDQ2hFLE1BQUs7b0RBQ0x5RixVQUFVckYsYUFBYUksaUJBQWlCNEIsTUFBTSxLQUFLO29EQUNuRDBCLFdBQVU7OERBRVQxRCxZQUFZLGdCQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7O2dDQU1wQ0YsZ0JBQWdCLG1CQUNmLDhEQUFDMkQ7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ0k7Z0RBQUlKLFdBQVU7Z0RBQTZDSyxNQUFLO2dEQUFPQyxRQUFPO2dEQUFlQyxTQUFROzBEQUNwRyw0RUFBQ0M7b0RBQUtDLGVBQWM7b0RBQVFDLGdCQUFlO29EQUFRQyxhQUFhO29EQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7O3NEQUd6RSw4REFBQ2dCOzRDQUFHNUIsV0FBVTtzREFBc0Q7Ozs7OztzREFHcEUsOERBQUMwQjs0Q0FBRTFCLFdBQVU7c0RBQW1DOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBV2xFO0dBOWpCd0JqRTs7UUFNbUJELHlEQUFPQTs7O0tBTjFCQyIsInNvdXJjZXMiOlsiL1VzZXJzL2dpb3JnaS9Eb2N1bWVudHMvYXVnbWVudC1wcm9qZWN0cy9wYXJhbm9ybWFsL2FwcC9jb21wb25lbnRzL0F1dGhNb2RhbC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSAnLi4vY29udGV4dC9BdXRoQ29udGV4dCc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEF1dGhNb2RhbCh7IGlzT3Blbiwgb25DbG9zZSwgdHlwZSwgb25Td2l0Y2hUeXBlIH0pIHtcbiAgY29uc3QgW2N1cnJlbnRTdGVwLCBzZXRDdXJyZW50U3RlcF0gPSB1c2VTdGF0ZSgxKTtcbiAgY29uc3QgW2lzTG9hZGluZywgc2V0SXNMb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2Vycm9ycywgc2V0RXJyb3JzXSA9IHVzZVN0YXRlKHt9KTtcbiAgY29uc3QgW3ZlcmlmaWNhdGlvbkNvZGUsIHNldFZlcmlmaWNhdGlvbkNvZGVdID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbaXNWZXJpZmljYXRpb25TZW50LCBzZXRJc1ZlcmlmaWNhdGlvblNlbnRdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCB7IGxvZ2luLCByZWdpc3RlciwgdmVyaWZ5UGhvbmUgfSA9IHVzZUF1dGgoKTtcblxuICAvLyBMb2dpbiBmb3JtIGRhdGFcbiAgY29uc3QgW2xvZ2luRGF0YSwgc2V0TG9naW5EYXRhXSA9IHVzZVN0YXRlKHtcbiAgICBlbWFpbDogJycsXG4gICAgcGFzc3dvcmQ6ICcnXG4gIH0pO1xuXG4gIC8vIFJlZ2lzdHJhdGlvbiBmb3JtIGRhdGFcbiAgY29uc3QgW3JlZ2lzdGVyRGF0YSwgc2V0UmVnaXN0ZXJEYXRhXSA9IHVzZVN0YXRlKHtcbiAgICBmaXJzdE5hbWU6ICcnLFxuICAgIGxhc3ROYW1lOiAnJyxcbiAgICBwaG9uZTogJycsXG4gICAgZmFjZWJvb2tVcmw6ICcnLFxuICAgIGdlbmRlcjogJycsXG4gICAgcGFzc3dvcmQ6ICcnLFxuICAgIGNvbmZpcm1QYXNzd29yZDogJycsXG4gICAgYWdlOiAnJyxcbiAgICB0ZXJtc0FjY2VwdGVkOiBmYWxzZVxuICB9KTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChpc09wZW4pIHtcbiAgICAgIHNldEN1cnJlbnRTdGVwKDEpO1xuICAgICAgc2V0RXJyb3JzKHt9KTtcbiAgICAgIHNldFZlcmlmaWNhdGlvbkNvZGUoJycpO1xuICAgICAgc2V0SXNWZXJpZmljYXRpb25TZW50KGZhbHNlKTtcbiAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9LCBbaXNPcGVuLCB0eXBlXSk7XG5cbiAgY29uc3QgdmFsaWRhdGVFbWFpbCA9IChlbWFpbCkgPT4ge1xuICAgIGNvbnN0IGVtYWlsUmVnZXggPSAvXlteXFxzQF0rQFteXFxzQF0rXFwuW15cXHNAXSskLztcbiAgICByZXR1cm4gZW1haWxSZWdleC50ZXN0KGVtYWlsKTtcbiAgfTtcblxuICBjb25zdCB2YWxpZGF0ZVBob25lID0gKHBob25lKSA9PiB7XG4gICAgY29uc3QgcGhvbmVSZWdleCA9IC9eKFxcKzk5NXw5OTUpP1swLTldezl9JC87XG4gICAgcmV0dXJuIHBob25lUmVnZXgudGVzdChwaG9uZS5yZXBsYWNlKC9cXHMvZywgJycpKTtcbiAgfTtcblxuICBjb25zdCB2YWxpZGF0ZVBhc3N3b3JkID0gKHBhc3N3b3JkKSA9PiB7XG4gICAgcmV0dXJuIHBhc3N3b3JkLmxlbmd0aCA+PSA4ICYmIFxuICAgICAgICAgICAvW0EtWl0vLnRlc3QocGFzc3dvcmQpICYmIFxuICAgICAgICAgICAvW2Etel0vLnRlc3QocGFzc3dvcmQpICYmIFxuICAgICAgICAgICAvWzAtOV0vLnRlc3QocGFzc3dvcmQpO1xuICB9O1xuXG4gIGNvbnN0IHZhbGlkYXRlRmFjZWJvb2tVcmwgPSAodXJsKSA9PiB7XG4gICAgaWYgKCF1cmwpIHJldHVybiB0cnVlOyAvLyBPcHRpb25hbCBmaWVsZFxuICAgIGNvbnN0IGZhY2Vib29rUmVnZXggPSAvXmh0dHBzPzpcXC9cXC8od3d3XFwuKT9mYWNlYm9va1xcLmNvbVxcL1thLXpBLVowLTkuXSskLztcbiAgICByZXR1cm4gZmFjZWJvb2tSZWdleC50ZXN0KHVybCk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlTG9naW5TdWJtaXQgPSBhc3luYyAoZSkgPT4ge1xuICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICBzZXRJc0xvYWRpbmcodHJ1ZSk7XG4gICAgc2V0RXJyb3JzKHt9KTtcblxuICAgIGNvbnN0IG5ld0Vycm9ycyA9IHt9O1xuICAgIGlmICghdmFsaWRhdGVFbWFpbChsb2dpbkRhdGEuZW1haWwpKSB7XG4gICAgICBuZXdFcnJvcnMuZW1haWwgPSAn4YOS4YOX4YOu4YOd4YOV4YOXIOGDqOGDlOGDmOGDp+GDleGDkOGDnOGDneGDlyDhg6Hhg6zhg53hg6Dhg5gg4YOU4YOaLeGDpOGDneGDoeGDouGDkCc7XG4gICAgfVxuICAgIGlmICghbG9naW5EYXRhLnBhc3N3b3JkKSB7XG4gICAgICBuZXdFcnJvcnMucGFzc3dvcmQgPSAn4YOe4YOQ4YOg4YOd4YOa4YOYIOGDkOGDo+GDquGDmOGDmuGDlOGDkeGDlOGDmuGDmOGDkCc7XG4gICAgfVxuXG4gICAgaWYgKE9iamVjdC5rZXlzKG5ld0Vycm9ycykubGVuZ3RoID4gMCkge1xuICAgICAgc2V0RXJyb3JzKG5ld0Vycm9ycyk7XG4gICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IGxvZ2luKGxvZ2luRGF0YS5lbWFpbCwgbG9naW5EYXRhLnBhc3N3b3JkKTtcbiAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xuXG4gICAgaWYgKHJlc3VsdC5zdWNjZXNzKSB7XG4gICAgICBvbkNsb3NlKCk7XG4gICAgfSBlbHNlIHtcbiAgICAgIHNldEVycm9ycyh7IGdlbmVyYWw6ICfhg5Dhg6Dhg5Dhg6Hhg6zhg53hg6Dhg5gg4YOU4YOaLeGDpOGDneGDoeGDouGDkCDhg5Dhg5wg4YOe4YOQ4YOg4YOd4YOa4YOYJyB9KTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlUmVnaXN0ZXJTdGVwMSA9IChlKSA9PiB7XG4gICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgIGNvbnN0IG5ld0Vycm9ycyA9IHt9O1xuXG4gICAgaWYgKCFyZWdpc3RlckRhdGEuZmlyc3ROYW1lLnRyaW0oKSkge1xuICAgICAgbmV3RXJyb3JzLmZpcnN0TmFtZSA9ICfhg6Hhg5Dhg67hg5Thg5rhg5gg4YOQ4YOj4YOq4YOY4YOa4YOU4YOR4YOU4YOa4YOY4YOQJztcbiAgICB9XG4gICAgaWYgKCFyZWdpc3RlckRhdGEubGFzdE5hbWUudHJpbSgpKSB7XG4gICAgICBuZXdFcnJvcnMubGFzdE5hbWUgPSAn4YOS4YOV4YOQ4YOg4YOYIOGDkOGDo+GDquGDmOGDmuGDlOGDkeGDlOGDmuGDmOGDkCc7XG4gICAgfVxuICAgIGlmICghdmFsaWRhdGVQaG9uZShyZWdpc3RlckRhdGEucGhvbmUpKSB7XG4gICAgICBuZXdFcnJvcnMucGhvbmUgPSAn4YOS4YOX4YOu4YOd4YOV4YOXIOGDqOGDlOGDmOGDp+GDleGDkOGDnOGDneGDlyDhg6Hhg6zhg53hg6Dhg5gg4YOi4YOU4YOa4YOU4YOk4YOd4YOc4YOY4YOhIOGDnOGDneGDm+GDlOGDoOGDmCc7XG4gICAgfVxuICAgIGlmICghdmFsaWRhdGVGYWNlYm9va1VybChyZWdpc3RlckRhdGEuZmFjZWJvb2tVcmwpKSB7XG4gICAgICBuZXdFcnJvcnMuZmFjZWJvb2tVcmwgPSAn4YOS4YOX4YOu4YOd4YOV4YOXIOGDqOGDlOGDmOGDp+GDleGDkOGDnOGDneGDlyDhg6Hhg6zhg53hg6Dhg5ggRmFjZWJvb2sgVVJMJztcbiAgICB9XG4gICAgaWYgKCFyZWdpc3RlckRhdGEuZ2VuZGVyKSB7XG4gICAgICBuZXdFcnJvcnMuZ2VuZGVyID0gJ+GDoeGDpeGDlOGDoeGDmOGDoSDhg5vhg5jhg5fhg5jhg5fhg5Thg5Hhg5Ag4YOQ4YOj4YOq4YOY4YOa4YOU4YOR4YOU4YOa4YOY4YOQJztcbiAgICB9XG5cbiAgICBpZiAoT2JqZWN0LmtleXMobmV3RXJyb3JzKS5sZW5ndGggPiAwKSB7XG4gICAgICBzZXRFcnJvcnMobmV3RXJyb3JzKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBzZXRFcnJvcnMoe30pO1xuICAgIHNldEN1cnJlbnRTdGVwKDIpO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVJlZ2lzdGVyU3RlcDIgPSBhc3luYyAoZSkgPT4ge1xuICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICBjb25zdCBuZXdFcnJvcnMgPSB7fTtcblxuICAgIGlmICghdmFsaWRhdGVQYXNzd29yZChyZWdpc3RlckRhdGEucGFzc3dvcmQpKSB7XG4gICAgICBuZXdFcnJvcnMucGFzc3dvcmQgPSAn4YOe4YOQ4YOg4YOd4YOa4YOYIOGDo+GDnOGDk+GDkCDhg6jhg5Thg5jhg6rhg5Dhg5Xhg5Phg5Thg6Eg4YOb4YOY4YOc4YOY4YOb4YOj4YObIDgg4YOh4YOY4YOb4YOR4YOd4YOa4YOd4YOhLCDhg5Phg5jhg5Mg4YOT4YOQIOGDnuGDkOGDouGDkOGDoOGDkCDhg5Dhg6Hhg53hg5Thg5Hhg6Eg4YOT4YOQIOGDquGDmOGDpOGDoOGDoSc7XG4gICAgfVxuICAgIGlmIChyZWdpc3RlckRhdGEucGFzc3dvcmQgIT09IHJlZ2lzdGVyRGF0YS5jb25maXJtUGFzc3dvcmQpIHtcbiAgICAgIG5ld0Vycm9ycy5jb25maXJtUGFzc3dvcmQgPSAn4YOe4YOQ4YOg4YOd4YOa4YOU4YOR4YOYIOGDkOGDoCDhg5Thg5vhg5fhg67hg5Xhg5Thg5Xhg5AnO1xuICAgIH1cbiAgICBpZiAoIXJlZ2lzdGVyRGF0YS50ZXJtc0FjY2VwdGVkKSB7XG4gICAgICBuZXdFcnJvcnMudGVybXNBY2NlcHRlZCA9ICfhg6zhg5Thg6Hhg5Thg5Hhg5jhg6Hhg5Ag4YOT4YOQIOGDnuGDmOGDoOGDneGDkeGDlOGDkeGDmOGDoSDhg5vhg5jhg6bhg5Thg5Hhg5Ag4YOQ4YOj4YOq4YOY4YOa4YOU4YOR4YOU4YOa4YOY4YOQJztcbiAgICB9XG5cbiAgICBpZiAoT2JqZWN0LmtleXMobmV3RXJyb3JzKS5sZW5ndGggPiAwKSB7XG4gICAgICBzZXRFcnJvcnMobmV3RXJyb3JzKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBzZXRJc0xvYWRpbmcodHJ1ZSk7XG4gICAgc2V0RXJyb3JzKHt9KTtcblxuICAgIC8vIFNpbXVsYXRlIHNlbmRpbmcgdmVyaWZpY2F0aW9uIGNvZGVcbiAgICBhd2FpdCBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgMTAwMCkpO1xuICAgIHNldElzVmVyaWZpY2F0aW9uU2VudCh0cnVlKTtcbiAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xuICAgIHNldEN1cnJlbnRTdGVwKDMpO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVBob25lVmVyaWZpY2F0aW9uID0gYXN5bmMgKGUpID0+IHtcbiAgICBlLnByZXZlbnREZWZhdWx0KCk7XG4gICAgaWYgKHZlcmlmaWNhdGlvbkNvZGUubGVuZ3RoICE9PSA2KSB7XG4gICAgICBzZXRFcnJvcnMoeyB2ZXJpZmljYXRpb246ICfhg5Lhg5fhg67hg53hg5Xhg5cg4YOo4YOU4YOY4YOn4YOV4YOQ4YOc4YOd4YOXIDYt4YOc4YOY4YOo4YOc4YOQIOGDmeGDneGDk+GDmCcgfSk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgc2V0SXNMb2FkaW5nKHRydWUpO1xuICAgIHNldEVycm9ycyh7fSk7XG5cbiAgICBjb25zdCB2ZXJpZnlSZXN1bHQgPSBhd2FpdCB2ZXJpZnlQaG9uZSh2ZXJpZmljYXRpb25Db2RlKTtcbiAgICBcbiAgICBpZiAodmVyaWZ5UmVzdWx0LnN1Y2Nlc3MpIHtcbiAgICAgIC8vIENvbXBsZXRlIHJlZ2lzdHJhdGlvblxuICAgICAgY29uc3QgdXNlckRhdGEgPSB7XG4gICAgICAgIG5hbWU6IGAke3JlZ2lzdGVyRGF0YS5maXJzdE5hbWV9ICR7cmVnaXN0ZXJEYXRhLmxhc3ROYW1lfWAsXG4gICAgICAgIGVtYWlsOiBgJHtyZWdpc3RlckRhdGEuZmlyc3ROYW1lLnRvTG93ZXJDYXNlKCl9LiR7cmVnaXN0ZXJEYXRhLmxhc3ROYW1lLnRvTG93ZXJDYXNlKCl9QGV4YW1wbGUuY29tYCwgLy8gTW9jayBlbWFpbFxuICAgICAgICBwaG9uZTogcmVnaXN0ZXJEYXRhLnBob25lLFxuICAgICAgICBmYWNlYm9va1VybDogcmVnaXN0ZXJEYXRhLmZhY2Vib29rVXJsLFxuICAgICAgICBnZW5kZXI6IHJlZ2lzdGVyRGF0YS5nZW5kZXIsXG4gICAgICAgIGFnZTogcmVnaXN0ZXJEYXRhLmFnZSB8fCBudWxsLFxuICAgICAgICBwYXNzd29yZDogcmVnaXN0ZXJEYXRhLnBhc3N3b3JkXG4gICAgICB9O1xuXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCByZWdpc3Rlcih1c2VyRGF0YSk7XG4gICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xuXG4gICAgICBpZiAocmVzdWx0LnN1Y2Nlc3MpIHtcbiAgICAgICAgc2V0Q3VycmVudFN0ZXAoNCk7IC8vIFN1Y2Nlc3Mgc3RlcFxuICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgICBvbkNsb3NlKCk7XG4gICAgICAgIH0sIDIwMDApO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc2V0RXJyb3JzKHsgZ2VuZXJhbDogJ+GDoOGDlOGDkuGDmOGDoeGDouGDoOGDkOGDquGDmOGDkCDhg5Xhg5Thg6Ag4YOT4YOQ4YOh4YOg4YOj4YOa4YOT4YOQJyB9KTtcbiAgICAgIH1cbiAgICB9IGVsc2Uge1xuICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcbiAgICAgIHNldEVycm9ycyh7IHZlcmlmaWNhdGlvbjogJ+GDkOGDoOGDkOGDoeGDrOGDneGDoOGDmCDhg5nhg53hg5Phg5gnIH0pO1xuICAgIH1cbiAgfTtcblxuICBpZiAoIWlzT3BlbikgcmV0dXJuIG51bGw7XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZpeGVkIGluc2V0LTAgei01MCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBwLTQgYmctYmxhY2svNTAgbW9kYWwtYmFja2Ryb3BcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgZGFyazpiZy1ncmF5LTgwMCByb3VuZGVkLXhsIHNoYWRvdy0yeGwgbWF4LXctbWQgdy1mdWxsIG1heC1oLVs5MHZoXSBvdmVyZmxvdy15LWF1dG8gYW5pbWF0ZS1zY2FsZS1pblwiPlxuICAgICAgICB7LyogSGVhZGVyICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBwLTYgYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwIGRhcms6Ym9yZGVyLWdyYXktNzAwXCI+XG4gICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktODAwIGRhcms6dGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAge3R5cGUgPT09ICdsb2dpbicgPyAn4YOo4YOU4YOh4YOV4YOa4YOQJyA6ICfhg6Dhg5Thg5Lhg5jhg6Hhg6Lhg6Dhg5Dhg6rhg5jhg5AnfVxuICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgb25DbGljaz17b25DbG9zZX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDAgaG92ZXI6dGV4dC1ncmF5LTcwMCBkYXJrOnRleHQtZ3JheS00MDAgZGFyazpob3Zlcjp0ZXh0LWdyYXktMjAwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNiBoLTZcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTYgMThMMTggNk02IDZsMTIgMTJcIiAvPlxuICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBQcm9ncmVzcyBiYXIgZm9yIHJlZ2lzdHJhdGlvbiAqL31cbiAgICAgICAge3R5cGUgPT09ICdyZWdpc3RlcicgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtNiBwdC00XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiB0ZXh0LXNtIHRleHQtZ3JheS01MDAgZGFyazp0ZXh0LWdyYXktNDAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgPHNwYW4+4YOc4YOQ4YOR4YOY4YOv4YOYIHtjdXJyZW50U3RlcH0gLyA0PC9zcGFuPlxuICAgICAgICAgICAgICA8c3Bhbj57TWF0aC5yb3VuZCgoY3VycmVudFN0ZXAgLyA0KSAqIDEwMCl9JTwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctZ3JheS0yMDAgZGFyazpiZy1ncmF5LTcwMCByb3VuZGVkLWZ1bGwgaC0yXCI+XG4gICAgICAgICAgICAgIDxkaXYgXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctcHJpbWFyeSBoLTIgcm91bmRlZC1mdWxsIHByb2dyZXNzLWJhclwiIFxuICAgICAgICAgICAgICAgIHN0eWxlPXt7IHdpZHRoOiBgJHsoY3VycmVudFN0ZXAgLyA0KSAqIDEwMH0lYCB9fVxuICAgICAgICAgICAgICA+PC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cblxuICAgICAgICB7LyogQ29udGVudCAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTZcIj5cbiAgICAgICAgICB7ZXJyb3JzLmdlbmVyYWwgJiYgKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi00IHAtMyBiZy1yZWQtMTAwIGRhcms6YmctcmVkLTkwMC8yMCBib3JkZXIgYm9yZGVyLXJlZC0zMDAgZGFyazpib3JkZXItcmVkLTcwMCByb3VuZGVkLWxnIHRleHQtcmVkLTcwMCBkYXJrOnRleHQtcmVkLTQwMCB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgIHtlcnJvcnMuZ2VuZXJhbH1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG5cbiAgICAgICAgICB7dHlwZSA9PT0gJ2xvZ2luJyA/IChcbiAgICAgICAgICAgIDxmb3JtIG9uU3VibWl0PXtoYW5kbGVMb2dpblN1Ym1pdH0gY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBkYXJrOnRleHQtZ3JheS0zMDAgbWItMlwiPlxuICAgICAgICAgICAgICAgICAg4YOU4YOaLeGDpOGDneGDoeGDouGDkFxuICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICB0eXBlPVwiZW1haWxcIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2xvZ2luRGF0YS5lbWFpbH1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0TG9naW5EYXRhKHsgLi4ubG9naW5EYXRhLCBlbWFpbDogZS50YXJnZXQudmFsdWUgfSl9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B3LWZ1bGwgcHgtMyBweS0yIGJvcmRlciByb3VuZGVkLWxnIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLXByaW1hcnkgZm9jdXM6Ym9yZGVyLXRyYW5zcGFyZW50IGRhcms6YmctZ3JheS03MDAgZGFyazp0ZXh0LXdoaXRlICR7XG4gICAgICAgICAgICAgICAgICAgIGVycm9ycy5lbWFpbCA/ICdpbnB1dC1lcnJvcicgOiAnYm9yZGVyLWdyYXktMzAwIGRhcms6Ym9yZGVyLWdyYXktNjAwJ1xuICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cInlvdXJAZW1haWwuY29tXCJcbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIHtlcnJvcnMuZW1haWwgJiYgPHAgY2xhc3NOYW1lPVwibXQtMSB0ZXh0LXNtIHRleHQtcmVkLTYwMCBkYXJrOnRleHQtcmVkLTQwMFwiPntlcnJvcnMuZW1haWx9PC9wPn1cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIGRhcms6dGV4dC1ncmF5LTMwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICDhg57hg5Dhg6Dhg53hg5rhg5hcbiAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgdHlwZT1cInBhc3N3b3JkXCJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtsb2dpbkRhdGEucGFzc3dvcmR9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldExvZ2luRGF0YSh7IC4uLmxvZ2luRGF0YSwgcGFzc3dvcmQ6IGUudGFyZ2V0LnZhbHVlIH0pfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdy1mdWxsIHB4LTMgcHktMiBib3JkZXIgcm91bmRlZC1sZyBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1wcmltYXJ5IGZvY3VzOmJvcmRlci10cmFuc3BhcmVudCBkYXJrOmJnLWdyYXktNzAwIGRhcms6dGV4dC13aGl0ZSAke1xuICAgICAgICAgICAgICAgICAgICBlcnJvcnMucGFzc3dvcmQgPyAnaW5wdXQtZXJyb3InIDogJ2JvcmRlci1ncmF5LTMwMCBkYXJrOmJvcmRlci1ncmF5LTYwMCdcbiAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLigKLigKLigKLigKLigKLigKLigKLigKJcIlxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAge2Vycm9ycy5wYXNzd29yZCAmJiA8cCBjbGFzc05hbWU9XCJtdC0xIHRleHQtc20gdGV4dC1yZWQtNjAwIGRhcms6dGV4dC1yZWQtNDAwXCI+e2Vycm9ycy5wYXNzd29yZH08L3A+fVxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgdHlwZT1cInN1Ym1pdFwiXG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzTG9hZGluZ31cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctcHJpbWFyeSBob3ZlcjpiZy1wcmltYXJ5LWRhcmsgdGV4dC13aGl0ZSBweS0yIHB4LTQgcm91bmRlZC1sZyBmb250LW1lZGl1bSB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWRcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAge2lzTG9hZGluZyA/ICfhg6jhg5Thg6Hhg5Xhg5rhg5AuLi4nIDogJ+GDqOGDlOGDoeGDleGDmuGDkCd9XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IG9uU3dpdGNoVHlwZSgncmVnaXN0ZXInKX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtcHJpbWFyeSBob3Zlcjp0ZXh0LXByaW1hcnktZGFyayB0ZXh0LXNtIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICDhg5Dhg6Ag4YOS4YOQ4YOl4YOV4YOXIOGDkOGDnOGDkuGDkOGDoOGDmOGDqOGDmD8g4YOT4YOQ4YOg4YOU4YOS4YOY4YOh4YOi4YOg4YOY4YOg4YOT4YOY4YOXXG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9mb3JtPlxuICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAvLyBSZWdpc3RyYXRpb24gZm9ybXMgYmFzZWQgb24gY3VycmVudCBzdGVwXG4gICAgICAgICAgICA8PlxuICAgICAgICAgICAgICB7Y3VycmVudFN0ZXAgPT09IDEgJiYgKFxuICAgICAgICAgICAgICAgIDxmb3JtIG9uU3VibWl0PXtoYW5kbGVSZWdpc3RlclN0ZXAxfSBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTgwMCBkYXJrOnRleHQtd2hpdGUgbWItNFwiPlxuICAgICAgICAgICAgICAgICAgICDhg57hg5jhg6Dhg5Dhg5Phg5gg4YOY4YOc4YOk4YOd4YOg4YOb4YOQ4YOq4YOY4YOQXG4gICAgICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIGRhcms6dGV4dC1ncmF5LTMwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICDhg6Hhg5Dhg67hg5Thg5rhg5ggKlxuICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17cmVnaXN0ZXJEYXRhLmZpcnN0TmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0UmVnaXN0ZXJEYXRhKHsgLi4ucmVnaXN0ZXJEYXRhLCBmaXJzdE5hbWU6IGUudGFyZ2V0LnZhbHVlIH0pfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdy1mdWxsIHB4LTMgcHktMiBib3JkZXIgcm91bmRlZC1sZyBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1wcmltYXJ5IGZvY3VzOmJvcmRlci10cmFuc3BhcmVudCBkYXJrOmJnLWdyYXktNzAwIGRhcms6dGV4dC13aGl0ZSAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICBlcnJvcnMuZmlyc3ROYW1lID8gJ2lucHV0LWVycm9yJyA6ICdib3JkZXItZ3JheS0zMDAgZGFyazpib3JkZXItZ3JheS02MDAnXG4gICAgICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgIHtlcnJvcnMuZmlyc3ROYW1lICYmIDxwIGNsYXNzTmFtZT1cIm10LTEgdGV4dC1zbSB0ZXh0LXJlZC02MDAgZGFyazp0ZXh0LXJlZC00MDBcIj57ZXJyb3JzLmZpcnN0TmFtZX08L3A+fVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgZGFyazp0ZXh0LWdyYXktMzAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIOGDkuGDleGDkOGDoOGDmCAqXG4gICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtyZWdpc3RlckRhdGEubGFzdE5hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFJlZ2lzdGVyRGF0YSh7IC4uLnJlZ2lzdGVyRGF0YSwgbGFzdE5hbWU6IGUudGFyZ2V0LnZhbHVlIH0pfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdy1mdWxsIHB4LTMgcHktMiBib3JkZXIgcm91bmRlZC1sZyBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1wcmltYXJ5IGZvY3VzOmJvcmRlci10cmFuc3BhcmVudCBkYXJrOmJnLWdyYXktNzAwIGRhcms6dGV4dC13aGl0ZSAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICBlcnJvcnMubGFzdE5hbWUgPyAnaW5wdXQtZXJyb3InIDogJ2JvcmRlci1ncmF5LTMwMCBkYXJrOmJvcmRlci1ncmF5LTYwMCdcbiAgICAgICAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAge2Vycm9ycy5sYXN0TmFtZSAmJiA8cCBjbGFzc05hbWU9XCJtdC0xIHRleHQtc20gdGV4dC1yZWQtNjAwIGRhcms6dGV4dC1yZWQtNDAwXCI+e2Vycm9ycy5sYXN0TmFtZX08L3A+fVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIGRhcms6dGV4dC1ncmF5LTMwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAg4YOi4YOU4YOa4YOU4YOk4YOd4YOc4YOY4YOhIOGDnOGDneGDm+GDlOGDoOGDmCAqXG4gICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZWxcIlxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtyZWdpc3RlckRhdGEucGhvbmV9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRSZWdpc3RlckRhdGEoeyAuLi5yZWdpc3RlckRhdGEsIHBob25lOiBlLnRhcmdldC52YWx1ZSB9KX1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B3LWZ1bGwgcHgtMyBweS0yIGJvcmRlciByb3VuZGVkLWxnIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLXByaW1hcnkgZm9jdXM6Ym9yZGVyLXRyYW5zcGFyZW50IGRhcms6YmctZ3JheS03MDAgZGFyazp0ZXh0LXdoaXRlICR7XG4gICAgICAgICAgICAgICAgICAgICAgICBlcnJvcnMucGhvbmUgPyAnaW5wdXQtZXJyb3InIDogJ2JvcmRlci1ncmF5LTMwMCBkYXJrOmJvcmRlci1ncmF5LTYwMCdcbiAgICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIis5OTUgNTU1IDEyMyA0NTZcIlxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICB7ZXJyb3JzLnBob25lICYmIDxwIGNsYXNzTmFtZT1cIm10LTEgdGV4dC1zbSB0ZXh0LXJlZC02MDAgZGFyazp0ZXh0LXJlZC00MDBcIj57ZXJyb3JzLnBob25lfTwvcD59XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBkYXJrOnRleHQtZ3JheS0zMDAgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIEZhY2Vib29rIOGDnuGDoOGDneGDpOGDmOGDmuGDmCAqXG4gICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ1cmxcIlxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtyZWdpc3RlckRhdGEuZmFjZWJvb2tVcmx9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRSZWdpc3RlckRhdGEoeyAuLi5yZWdpc3RlckRhdGEsIGZhY2Vib29rVXJsOiBlLnRhcmdldC52YWx1ZSB9KX1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B3LWZ1bGwgcHgtMyBweS0yIGJvcmRlciByb3VuZGVkLWxnIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLXByaW1hcnkgZm9jdXM6Ym9yZGVyLXRyYW5zcGFyZW50IGRhcms6YmctZ3JheS03MDAgZGFyazp0ZXh0LXdoaXRlICR7XG4gICAgICAgICAgICAgICAgICAgICAgICBlcnJvcnMuZmFjZWJvb2tVcmwgPyAnaW5wdXQtZXJyb3InIDogJ2JvcmRlci1ncmF5LTMwMCBkYXJrOmJvcmRlci1ncmF5LTYwMCdcbiAgICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cImh0dHBzOi8vZmFjZWJvb2suY29tL3lvdXJwcm9maWxlXCJcbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAge2Vycm9ycy5mYWNlYm9va1VybCAmJiA8cCBjbGFzc05hbWU9XCJtdC0xIHRleHQtc20gdGV4dC1yZWQtNjAwIGRhcms6dGV4dC1yZWQtNDAwXCI+e2Vycm9ycy5mYWNlYm9va1VybH08L3A+fVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgZGFyazp0ZXh0LWdyYXktMzAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIOGDoeGDpeGDlOGDoeGDmCAqXG4gICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17cmVnaXN0ZXJEYXRhLmdlbmRlcn1cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0UmVnaXN0ZXJEYXRhKHsgLi4ucmVnaXN0ZXJEYXRhLCBnZW5kZXI6IGUudGFyZ2V0LnZhbHVlIH0pfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdy1mdWxsIHB4LTMgcHktMiBib3JkZXIgcm91bmRlZC1sZyBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1wcmltYXJ5IGZvY3VzOmJvcmRlci10cmFuc3BhcmVudCBkYXJrOmJnLWdyYXktNzAwIGRhcms6dGV4dC13aGl0ZSAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICBlcnJvcnMuZ2VuZGVyID8gJ2lucHV0LWVycm9yJyA6ICdib3JkZXItZ3JheS0zMDAgZGFyazpib3JkZXItZ3JheS02MDAnXG4gICAgICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiXCI+4YOQ4YOY4YOg4YOp4YOY4YOU4YOXPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwibWFsZVwiPuGDm+GDkOGDm+GDoOGDneGDkeGDmOGDl+GDmDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImZlbWFsZVwiPuGDm+GDk+GDlOGDk+GDoOGDneGDkeGDmOGDl+GDmDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIm90aGVyXCI+4YOh4YOu4YOV4YOQPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgICAgICAgICAgICAge2Vycm9ycy5nZW5kZXIgJiYgPHAgY2xhc3NOYW1lPVwibXQtMSB0ZXh0LXNtIHRleHQtcmVkLTYwMCBkYXJrOnRleHQtcmVkLTQwMFwiPntlcnJvcnMuZ2VuZGVyfTwvcD59XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBkYXJrOnRleHQtZ3JheS0zMDAgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAg4YOQ4YOh4YOQ4YOZ4YOYXG4gICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJudW1iZXJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgbWluPVwiMTZcIlxuICAgICAgICAgICAgICAgICAgICAgICAgbWF4PVwiMTAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtyZWdpc3RlckRhdGEuYWdlfVxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRSZWdpc3RlckRhdGEoeyAuLi5yZWdpc3RlckRhdGEsIGFnZTogZS50YXJnZXQudmFsdWUgfSl9XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtMyBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgZGFyazpib3JkZXItZ3JheS02MDAgcm91bmRlZC1sZyBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1wcmltYXJ5IGZvY3VzOmJvcmRlci10cmFuc3BhcmVudCBkYXJrOmJnLWdyYXktNzAwIGRhcms6dGV4dC13aGl0ZVwiXG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwic3VibWl0XCJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGJnLXByaW1hcnkgaG92ZXI6YmctcHJpbWFyeS1kYXJrIHRleHQtd2hpdGUgcHktMiBweC00IHJvdW5kZWQtbGcgZm9udC1tZWRpdW0gdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAg4YOo4YOU4YOb4YOT4YOU4YOS4YOYIOGDnOGDkOGDkeGDmOGDr+GDmFxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IG9uU3dpdGNoVHlwZSgnbG9naW4nKX1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXByaW1hcnkgaG92ZXI6dGV4dC1wcmltYXJ5LWRhcmsgdGV4dC1zbSB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICDhg6Phg5nhg5Xhg5Qg4YOS4YOQ4YOl4YOV4YOXIOGDkOGDnOGDkuGDkOGDoOGDmOGDqOGDmD8g4YOo4YOU4YOT4YOY4YOXXG4gICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9mb3JtPlxuICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgIHtjdXJyZW50U3RlcCA9PT0gMiAmJiAoXG4gICAgICAgICAgICAgICAgPGZvcm0gb25TdWJtaXQ9e2hhbmRsZVJlZ2lzdGVyU3RlcDJ9IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktODAwIGRhcms6dGV4dC13aGl0ZSBtYi00XCI+XG4gICAgICAgICAgICAgICAgICAgIOGDnuGDkOGDoOGDneGDmuGDmOGDoSDhg6jhg5Thg6Xhg5vhg5zhg5BcbiAgICAgICAgICAgICAgICAgIDwvaDM+XG5cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgZGFyazp0ZXh0LWdyYXktMzAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICDhg57hg5Dhg6Dhg53hg5rhg5ggKlxuICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwicGFzc3dvcmRcIlxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtyZWdpc3RlckRhdGEucGFzc3dvcmR9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRSZWdpc3RlckRhdGEoeyAuLi5yZWdpc3RlckRhdGEsIHBhc3N3b3JkOiBlLnRhcmdldC52YWx1ZSB9KX1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B3LWZ1bGwgcHgtMyBweS0yIGJvcmRlciByb3VuZGVkLWxnIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLXByaW1hcnkgZm9jdXM6Ym9yZGVyLXRyYW5zcGFyZW50IGRhcms6YmctZ3JheS03MDAgZGFyazp0ZXh0LXdoaXRlICR7XG4gICAgICAgICAgICAgICAgICAgICAgICBlcnJvcnMucGFzc3dvcmQgPyAnaW5wdXQtZXJyb3InIDogJ2JvcmRlci1ncmF5LTMwMCBkYXJrOmJvcmRlci1ncmF5LTYwMCdcbiAgICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIuKAouKAouKAouKAouKAouKAouKAouKAolwiXG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgIHtlcnJvcnMucGFzc3dvcmQgJiYgPHAgY2xhc3NOYW1lPVwibXQtMSB0ZXh0LXNtIHRleHQtcmVkLTYwMCBkYXJrOnRleHQtcmVkLTQwMFwiPntlcnJvcnMucGFzc3dvcmR9PC9wPn1cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwibXQtMSB0ZXh0LXhzIHRleHQtZ3JheS01MDAgZGFyazp0ZXh0LWdyYXktNDAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAg4YOb4YOY4YOc4YOY4YOb4YOj4YObIDgg4YOh4YOY4YOb4YOR4YOd4YOa4YOdLCDhg5Phg5jhg5Phg5gg4YOT4YOQIOGDnuGDkOGDouGDkOGDoOGDkCDhg5Dhg6Hhg53hg5Thg5Hhg5gsIOGDquGDmOGDpOGDoOGDmFxuICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBkYXJrOnRleHQtZ3JheS0zMDAgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIOGDnuGDkOGDoOGDneGDmuGDmOGDoSDhg5Phg5Dhg5Phg5Dhg6Hhg6Lhg6Phg6Dhg5Thg5Hhg5AgKlxuICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwicGFzc3dvcmRcIlxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtyZWdpc3RlckRhdGEuY29uZmlybVBhc3N3b3JkfVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0UmVnaXN0ZXJEYXRhKHsgLi4ucmVnaXN0ZXJEYXRhLCBjb25maXJtUGFzc3dvcmQ6IGUudGFyZ2V0LnZhbHVlIH0pfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHctZnVsbCBweC0zIHB5LTIgYm9yZGVyIHJvdW5kZWQtbGcgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctcHJpbWFyeSBmb2N1czpib3JkZXItdHJhbnNwYXJlbnQgZGFyazpiZy1ncmF5LTcwMCBkYXJrOnRleHQtd2hpdGUgJHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGVycm9ycy5jb25maXJtUGFzc3dvcmQgPyAnaW5wdXQtZXJyb3InIDogJ2JvcmRlci1ncmF5LTMwMCBkYXJrOmJvcmRlci1ncmF5LTYwMCdcbiAgICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIuKAouKAouKAouKAouKAouKAouKAouKAolwiXG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgIHtlcnJvcnMuY29uZmlybVBhc3N3b3JkICYmIDxwIGNsYXNzTmFtZT1cIm10LTEgdGV4dC1zbSB0ZXh0LXJlZC02MDAgZGFyazp0ZXh0LXJlZC00MDBcIj57ZXJyb3JzLmNvbmZpcm1QYXNzd29yZH08L3A+fVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImNoZWNrYm94XCJcbiAgICAgICAgICAgICAgICAgICAgICBpZD1cInRlcm1zXCJcbiAgICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtyZWdpc3RlckRhdGEudGVybXNBY2NlcHRlZH1cbiAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFJlZ2lzdGVyRGF0YSh7IC4uLnJlZ2lzdGVyRGF0YSwgdGVybXNBY2NlcHRlZDogZS50YXJnZXQuY2hlY2tlZCB9KX1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtdC0xIHctNCBoLTQgdGV4dC1wcmltYXJ5IGJvcmRlci1ncmF5LTMwMCByb3VuZGVkIGZvY3VzOnJpbmctcHJpbWFyeVwiXG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgIDxsYWJlbCBodG1sRm9yPVwidGVybXNcIiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS03MDAgZGFyazp0ZXh0LWdyYXktMzAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAg4YOV4YOU4YOX4YOQ4YOc4YOu4YOb4YOU4YOR4YOYeycgJ31cbiAgICAgICAgICAgICAgICAgICAgICA8YSBocmVmPVwiI1wiIGNsYXNzTmFtZT1cInRleHQtcHJpbWFyeSBob3Zlcjp0ZXh0LXByaW1hcnktZGFya1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAg4YOs4YOU4YOh4YOU4YOR4YOh4YOQIOGDk+GDkCDhg57hg5jhg6Dhg53hg5Hhg5Thg5Hhg6FcbiAgICAgICAgICAgICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIHtlcnJvcnMudGVybXNBY2NlcHRlZCAmJiA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtcmVkLTYwMCBkYXJrOnRleHQtcmVkLTQwMFwiPntlcnJvcnMudGVybXNBY2NlcHRlZH08L3A+fVxuXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRDdXJyZW50U3RlcCgxKX1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4LTEgYmctZ3JheS0zMDAgaG92ZXI6YmctZ3JheS00MDAgZGFyazpiZy1ncmF5LTYwMCBkYXJrOmhvdmVyOmJnLWdyYXktNTAwIHRleHQtZ3JheS03MDAgZGFyazp0ZXh0LWdyYXktMjAwIHB5LTIgcHgtNCByb3VuZGVkLWxnIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMFwiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICDhg6Phg5nhg5Dhg5xcbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwic3VibWl0XCJcbiAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17aXNMb2FkaW5nfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXgtMSBiZy1wcmltYXJ5IGhvdmVyOmJnLXByaW1hcnktZGFyayB0ZXh0LXdoaXRlIHB5LTIgcHgtNCByb3VuZGVkLWxnIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBkaXNhYmxlZDpvcGFjaXR5LTUwXCJcbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIHtpc0xvYWRpbmcgPyAn4YOY4YOS4YOW4YOQ4YOV4YOc4YOU4YOR4YOQLi4uJyA6ICfhg5nhg53hg5Phg5jhg6Eg4YOS4YOQ4YOS4YOW4YOQ4YOV4YOc4YOQJ31cbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Zvcm0+XG4gICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAge2N1cnJlbnRTdGVwID09PSAzICYmIChcbiAgICAgICAgICAgICAgICA8Zm9ybSBvblN1Ym1pdD17aGFuZGxlUGhvbmVWZXJpZmljYXRpb259IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktODAwIGRhcms6dGV4dC13aGl0ZSBtYi00XCI+XG4gICAgICAgICAgICAgICAgICAgIOGDouGDlOGDmuGDlOGDpOGDneGDnOGDmOGDoSDhg5Phg5Dhg5Phg5Dhg6Hhg6Lhg6Phg6Dhg5Thg5Hhg5BcbiAgICAgICAgICAgICAgICAgIDwvaDM+XG5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwIGRhcms6dGV4dC1ncmF5LTQwMCBtYi00XCI+XG4gICAgICAgICAgICAgICAgICAgICAg4YOT4YOQ4YOT4YOQ4YOh4YOi4YOj4YOg4YOU4YOR4YOY4YOhIOGDmeGDneGDk+GDmCDhg5Lhg5Dhg5jhg5Lhg5bhg5Dhg5Xhg5zhg5Ag4YOc4YOd4YOb4YOU4YOg4YOW4YOUOlxuICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTgwMCBkYXJrOnRleHQtd2hpdGUgbWItNlwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtyZWdpc3RlckRhdGEucGhvbmV9XG4gICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIGRhcms6dGV4dC1ncmF5LTMwMCBtYi0yIHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAg4YOo4YOU4YOY4YOn4YOV4YOQ4YOc4YOU4YOXIDYt4YOc4YOY4YOo4YOc4YOQIOGDmeGDneGDk+GDmFxuICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgICAgbWF4TGVuZ3RoPVwiNlwiXG4gICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3ZlcmlmaWNhdGlvbkNvZGV9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRWZXJpZmljYXRpb25Db2RlKGUudGFyZ2V0LnZhbHVlLnJlcGxhY2UoL1xcRC9nLCAnJykpfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHZlcmlmaWNhdGlvbi1pbnB1dCB3LWZ1bGwgcHgtMyBweS0zIGJvcmRlciByb3VuZGVkLWxnIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLXByaW1hcnkgZm9jdXM6Ym9yZGVyLXRyYW5zcGFyZW50IGRhcms6YmctZ3JheS03MDAgZGFyazp0ZXh0LXdoaXRlICR7XG4gICAgICAgICAgICAgICAgICAgICAgICBlcnJvcnMudmVyaWZpY2F0aW9uID8gJ2lucHV0LWVycm9yJyA6ICdib3JkZXItZ3JheS0zMDAgZGFyazpib3JkZXItZ3JheS02MDAnXG4gICAgICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCIxMjM0NTZcIlxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICB7ZXJyb3JzLnZlcmlmaWNhdGlvbiAmJiA8cCBjbGFzc05hbWU9XCJtdC0xIHRleHQtc20gdGV4dC1yZWQtNjAwIGRhcms6dGV4dC1yZWQtNDAwIHRleHQtY2VudGVyXCI+e2Vycm9ycy52ZXJpZmljYXRpb259PC9wPn1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtcHJpbWFyeSBob3Zlcjp0ZXh0LXByaW1hcnktZGFyayB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgc2V0SXNWZXJpZmljYXRpb25TZW50KGZhbHNlKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIFJlc2VuZCBjb2RlIGxvZ2ljIGhlcmVcbiAgICAgICAgICAgICAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gc2V0SXNWZXJpZmljYXRpb25TZW50KHRydWUpLCAxMDAwKTtcbiAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAg4YOZ4YOd4YOT4YOY4YOhIOGDruGDlOGDmuGDkOGDruGDmuGDkCDhg5Lhg5Dhg5Lhg5bhg5Dhg5Xhg5zhg5BcbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0Q3VycmVudFN0ZXAoMil9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC0xIGJnLWdyYXktMzAwIGhvdmVyOmJnLWdyYXktNDAwIGRhcms6YmctZ3JheS02MDAgZGFyazpob3ZlcjpiZy1ncmF5LTUwMCB0ZXh0LWdyYXktNzAwIGRhcms6dGV4dC1ncmF5LTIwMCBweS0yIHB4LTQgcm91bmRlZC1sZyBmb250LW1lZGl1bSB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDBcIlxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAg4YOj4YOZ4YOQ4YOcXG4gICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInN1Ym1pdFwiXG4gICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzTG9hZGluZyB8fCB2ZXJpZmljYXRpb25Db2RlLmxlbmd0aCAhPT0gNn1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4LTEgYmctcHJpbWFyeSBob3ZlcjpiZy1wcmltYXJ5LWRhcmsgdGV4dC13aGl0ZSBweS0yIHB4LTQgcm91bmRlZC1sZyBmb250LW1lZGl1bSB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgZGlzYWJsZWQ6b3BhY2l0eS01MFwiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICB7aXNMb2FkaW5nID8gJ+GDm+GDneGDrOGDm+GDk+GDlOGDkeGDkC4uLicgOiAn4YOT4YOQ4YOT4YOQ4YOh4YOi4YOj4YOg4YOU4YOR4YOQJ31cbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Zvcm0+XG4gICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAge2N1cnJlbnRTdGVwID09PSA0ICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTE2IGgtMTYgYmctZ3JlZW4tMTAwIGRhcms6YmctZ3JlZW4tOTAwLzIwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBteC1hdXRvXCI+XG4gICAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy04IGgtOCB0ZXh0LWdyZWVuLTYwMCBkYXJrOnRleHQtZ3JlZW4tNDAwXCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTUgMTNsNCA0TDE5IDdcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktODAwIGRhcms6dGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICAgICAgICDhg6Dhg5Thg5Lhg5jhg6Hhg6Lhg6Dhg5Dhg6rhg5jhg5Ag4YOs4YOQ4YOg4YOb4YOQ4YOi4YOU4YOR4YOY4YOXIOGDk+GDkOGDoeGDoOGDo+GDmuGDk+GDkCFcbiAgICAgICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIGRhcms6dGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgICAgICAgICAgICDhg5nhg5Thg5fhg5jhg5rhg5gg4YOY4YOn4YOd4YOhIOGDl+GDpeGDleGDlOGDnOGDmCDhg5vhg53hg5Hhg6Dhg6vhg5Dhg5zhg5Thg5Hhg5Ag4YOp4YOV4YOU4YOc4YOhIOGDoeGDkOGDluGDneGDkuGDkOGDk+GDneGDlOGDkeGDkOGDqOGDmFxuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC8+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZUF1dGgiLCJBdXRoTW9kYWwiLCJpc09wZW4iLCJvbkNsb3NlIiwidHlwZSIsIm9uU3dpdGNoVHlwZSIsImN1cnJlbnRTdGVwIiwic2V0Q3VycmVudFN0ZXAiLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJlcnJvcnMiLCJzZXRFcnJvcnMiLCJ2ZXJpZmljYXRpb25Db2RlIiwic2V0VmVyaWZpY2F0aW9uQ29kZSIsImlzVmVyaWZpY2F0aW9uU2VudCIsInNldElzVmVyaWZpY2F0aW9uU2VudCIsImxvZ2luIiwicmVnaXN0ZXIiLCJ2ZXJpZnlQaG9uZSIsImxvZ2luRGF0YSIsInNldExvZ2luRGF0YSIsImVtYWlsIiwicGFzc3dvcmQiLCJyZWdpc3RlckRhdGEiLCJzZXRSZWdpc3RlckRhdGEiLCJmaXJzdE5hbWUiLCJsYXN0TmFtZSIsInBob25lIiwiZmFjZWJvb2tVcmwiLCJnZW5kZXIiLCJjb25maXJtUGFzc3dvcmQiLCJhZ2UiLCJ0ZXJtc0FjY2VwdGVkIiwidmFsaWRhdGVFbWFpbCIsImVtYWlsUmVnZXgiLCJ0ZXN0IiwidmFsaWRhdGVQaG9uZSIsInBob25lUmVnZXgiLCJyZXBsYWNlIiwidmFsaWRhdGVQYXNzd29yZCIsImxlbmd0aCIsInZhbGlkYXRlRmFjZWJvb2tVcmwiLCJ1cmwiLCJmYWNlYm9va1JlZ2V4IiwiaGFuZGxlTG9naW5TdWJtaXQiLCJlIiwicHJldmVudERlZmF1bHQiLCJuZXdFcnJvcnMiLCJPYmplY3QiLCJrZXlzIiwicmVzdWx0Iiwic3VjY2VzcyIsImdlbmVyYWwiLCJoYW5kbGVSZWdpc3RlclN0ZXAxIiwidHJpbSIsImhhbmRsZVJlZ2lzdGVyU3RlcDIiLCJQcm9taXNlIiwicmVzb2x2ZSIsInNldFRpbWVvdXQiLCJoYW5kbGVQaG9uZVZlcmlmaWNhdGlvbiIsInZlcmlmaWNhdGlvbiIsInZlcmlmeVJlc3VsdCIsInVzZXJEYXRhIiwibmFtZSIsInRvTG93ZXJDYXNlIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDIiLCJidXR0b24iLCJvbkNsaWNrIiwic3ZnIiwiZmlsbCIsInN0cm9rZSIsInZpZXdCb3giLCJwYXRoIiwic3Ryb2tlTGluZWNhcCIsInN0cm9rZUxpbmVqb2luIiwic3Ryb2tlV2lkdGgiLCJkIiwic3BhbiIsIk1hdGgiLCJyb3VuZCIsInN0eWxlIiwid2lkdGgiLCJmb3JtIiwib25TdWJtaXQiLCJsYWJlbCIsImlucHV0IiwidmFsdWUiLCJvbkNoYW5nZSIsInRhcmdldCIsInBsYWNlaG9sZGVyIiwicCIsImRpc2FibGVkIiwiaDMiLCJzZWxlY3QiLCJvcHRpb24iLCJtaW4iLCJtYXgiLCJpZCIsImNoZWNrZWQiLCJodG1sRm9yIiwiYSIsImhyZWYiLCJtYXhMZW5ndGgiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/AuthModal.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/components/Navigation.js":
/*!**************************************!*\
  !*** ./app/components/Navigation.js ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../context/AuthContext */ \"(app-pages-browser)/./app/context/AuthContext.js\");\n/* harmony import */ var _AuthModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AuthModal */ \"(app-pages-browser)/./app/components/AuthModal.js\");\n/* harmony import */ var _UserProfile__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./UserProfile */ \"(app-pages-browser)/./app/components/UserProfile.js\");\n/* harmony import */ var _ThemeToggle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ThemeToggle */ \"(app-pages-browser)/./app/components/ThemeToggle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction Navigation() {\n    _s();\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAuthModalOpen, setIsAuthModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [authModalType, setAuthModalType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('login'); // 'login' or 'register'\n    const { isAuthenticated, user } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navigation.useEffect\": ()=>{\n            const handleScroll = {\n                \"Navigation.useEffect.handleScroll\": ()=>{\n                    setIsScrolled(window.scrollY > 20);\n                }\n            }[\"Navigation.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"Navigation.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"Navigation.useEffect\"];\n        }\n    }[\"Navigation.useEffect\"], []);\n    const openAuthModal = (type)=>{\n        setAuthModalType(type);\n        setIsAuthModalOpen(true);\n        setIsMobileMenuOpen(false);\n    };\n    const scrollToSection = (sectionId)=>{\n        const element = document.getElementById(sectionId);\n        if (element) {\n            element.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n        setIsMobileMenuOpen(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"fixed top-0 left-0 right-0 z-40 transition-all duration-300 \".concat(isScrolled ? 'bg-white/90 dark:bg-gray-900/90 nav-backdrop shadow-lg' : 'bg-transparent'),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between h-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>scrollToSection('hero'),\n                                        className: \"flex items-center space-x-2 text-xl font-bold text-gray-800 dark:text-white hover:text-primary dark:hover:text-primary transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl\",\n                                                children: \"\\uD83C\\uDF32\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/Navigation.js\",\n                                                lineNumber: 54,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"ბუნებაში გასვლა\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/Navigation.js\",\n                                                lineNumber: 55,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/Navigation.js\",\n                                        lineNumber: 50,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/Navigation.js\",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden md:block\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-10 flex items-baseline space-x-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>scrollToSection('hero'),\n                                                className: \"text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary px-3 py-2 text-sm font-medium transition-colors\",\n                                                children: \"მთავარი\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/Navigation.js\",\n                                                lineNumber: 62,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>scrollToSection('trips-section'),\n                                                className: \"text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary px-3 py-2 text-sm font-medium transition-colors\",\n                                                children: \"გასვლები\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/Navigation.js\",\n                                                lineNumber: 68,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>scrollToSection('photos'),\n                                                className: \"text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary px-3 py-2 text-sm font-medium transition-colors\",\n                                                children: \"ფოტოები\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/Navigation.js\",\n                                                lineNumber: 74,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>scrollToSection('about'),\n                                                className: \"text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary px-3 py-2 text-sm font-medium transition-colors\",\n                                                children: \"ჩვენს შესახებ\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/Navigation.js\",\n                                                lineNumber: 80,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>scrollToSection('contact'),\n                                                className: \"text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary px-3 py-2 text-sm font-medium transition-colors\",\n                                                children: \"კონტაქტი\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/Navigation.js\",\n                                                lineNumber: 86,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/Navigation.js\",\n                                        lineNumber: 61,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/Navigation.js\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden md:flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ThemeToggle__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/Navigation.js\",\n                                            lineNumber: 97,\n                                            columnNumber: 15\n                                        }, this),\n                                        isAuthenticated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UserProfile__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            user: user\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/Navigation.js\",\n                                            lineNumber: 100,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>openAuthModal('login'),\n                                                    className: \"text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary px-4 py-2 text-sm font-medium transition-colors\",\n                                                    children: \"შესვლა\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/Navigation.js\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>openAuthModal('register'),\n                                                    className: \"bg-primary hover:bg-primary-dark text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 transform hover:scale-105\",\n                                                    children: \"რეგისტრაცია\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/Navigation.js\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/Navigation.js\",\n                                            lineNumber: 102,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/Navigation.js\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"md:hidden flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ThemeToggle__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/Navigation.js\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsMobileMenuOpen(!isMobileMenuOpen),\n                                            className: \"text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary p-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: isMobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M6 18L18 6M6 6l12 12\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/Navigation.js\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M4 6h16M4 12h16M4 18h16\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/Navigation.js\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/Navigation.js\",\n                                                lineNumber: 126,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/Navigation.js\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/Navigation.js\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/Navigation.js\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, this),\n                        isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden animate-fade-in\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-2 pt-2 pb-3 space-y-1 bg-white/95 dark:bg-gray-900/95 nav-backdrop rounded-lg mt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>scrollToSection('hero'),\n                                        className: \"block w-full text-left px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-colors\",\n                                        children: \"მთავარი\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/Navigation.js\",\n                                        lineNumber: 141,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>scrollToSection('trips-section'),\n                                        className: \"block w-full text-left px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-colors\",\n                                        children: \"გასვლები\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/Navigation.js\",\n                                        lineNumber: 147,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>scrollToSection('photos'),\n                                        className: \"block w-full text-left px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-colors\",\n                                        children: \"ფოტოები\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/Navigation.js\",\n                                        lineNumber: 153,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>scrollToSection('about'),\n                                        className: \"block w-full text-left px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-colors\",\n                                        children: \"ჩვენს შესახებ\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/Navigation.js\",\n                                        lineNumber: 159,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>scrollToSection('contact'),\n                                        className: \"block w-full text-left px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-colors\",\n                                        children: \"კონტაქტი\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/Navigation.js\",\n                                        lineNumber: 165,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border-t border-gray-200 dark:border-gray-700 pt-3 mt-3\",\n                                        children: isAuthenticated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UserProfile__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            user: user,\n                                            isMobile: true\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/Navigation.js\",\n                                            lineNumber: 175,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>openAuthModal('login'),\n                                                    className: \"block w-full text-left px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-colors\",\n                                                    children: \"შესვლა\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/Navigation.js\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>openAuthModal('register'),\n                                                    className: \"block w-full bg-primary hover:bg-primary-dark text-white px-3 py-2 rounded-lg text-base font-medium transition-all duration-300\",\n                                                    children: \"რეგისტრაცია\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/Navigation.js\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/Navigation.js\",\n                                            lineNumber: 177,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/Navigation.js\",\n                                        lineNumber: 173,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/Navigation.js\",\n                                lineNumber: 140,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/Navigation.js\",\n                            lineNumber: 139,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/Navigation.js\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/Navigation.js\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AuthModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: isAuthModalOpen,\n                onClose: ()=>setIsAuthModalOpen(false),\n                type: authModalType,\n                onSwitchType: setAuthModalType\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/Navigation.js\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Navigation, \"Uv/e6TMq5jSJyl+myG4hqNMBSiE=\", false, function() {\n    return [\n        _context_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = Navigation;\nvar _c;\n$RefreshReg$(_c, \"Navigation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/Navigation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/components/UserProfile.js":
/*!***************************************!*\
  !*** ./app/components/UserProfile.js ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ UserProfile)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../context/AuthContext */ \"(app-pages-browser)/./app/context/AuthContext.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction UserProfile(param) {\n    let { user, isMobile = false } = param;\n    _s();\n    const [isDropdownOpen, setIsDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { logout } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UserProfile.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"UserProfile.useEffect.handleClickOutside\": (event)=>{\n                    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                        setIsDropdownOpen(false);\n                    }\n                }\n            }[\"UserProfile.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"UserProfile.useEffect\": ()=>document.removeEventListener('mousedown', handleClickOutside)\n            })[\"UserProfile.useEffect\"];\n        }\n    }[\"UserProfile.useEffect\"], []);\n    const handleLogout = ()=>{\n        logout();\n        setIsDropdownOpen(false);\n    };\n    const getInitials = (name)=>{\n        return name.split(' ').map((word)=>word.charAt(0)).join('').toUpperCase().slice(0, 2);\n    };\n    if (isMobile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-2\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center px-3 py-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-semibold\",\n                                children: user.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: user.avatar,\n                                    alt: user.name,\n                                    className: \"w-8 h-8 rounded-full object-cover\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n                                    lineNumber: 43,\n                                    columnNumber: 17\n                                }, this) : getInitials(user.name)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n                                lineNumber: 41,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-medium text-gray-800 dark:text-white\",\n                                        children: user.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n                                        lineNumber: 49,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                        children: user.email\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n                                        lineNumber: 50,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n                                lineNumber: 48,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n                        lineNumber: 40,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>{},\n                    className: \"block w-full text-left px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-colors\",\n                    children: \"პროფილი\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>{},\n                    className: \"block w-full text-left px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-colors\",\n                    children: \"ჩემი გასვლები\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleLogout,\n                    className: \"block w-full text-left px-3 py-2 text-sm text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 transition-colors\",\n                    children: \"გასვლა\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n            lineNumber: 38,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        ref: dropdownRef,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsDropdownOpen(!isDropdownOpen),\n                className: \"flex items-center space-x-2 text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-all duration-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-semibold hover:bg-primary-dark transition-colors\",\n                        children: user.avatar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: user.avatar,\n                            alt: user.name,\n                            className: \"w-8 h-8 rounded-full object-cover\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n                            lineNumber: 87,\n                            columnNumber: 13\n                        }, this) : getInitials(user.name)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-700 dark:text-gray-300 font-medium hidden lg:block\",\n                        children: user.name.split(' ')[0]\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-gray-500 dark:text-gray-400 transition-transform duration-200 \".concat(isDropdownOpen ? 'rotate-180' : ''),\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M19 9l-7 7-7-7\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            isDropdownOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 mt-2 w-56 bg-white dark:bg-gray-800 rounded-lg shadow-lg ring-1 ring-black ring-opacity-5 dark:ring-gray-700 animate-scale-in z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"py-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-3 border-b border-gray-200 dark:border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-medium text-gray-800 dark:text-white\",\n                                    children: user.name\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n                                    lineNumber: 112,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-500 dark:text-gray-400 truncate\",\n                                    children: user.email\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n                                    lineNumber: 113,\n                                    columnNumber: 15\n                                }, this),\n                                !user.isVerified && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-yellow-600 dark:text-yellow-400 mt-1\",\n                                    children: \"⚠️ ტელეფონი არ არის დადასტურებული\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n                                    lineNumber: 115,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n                            lineNumber: 111,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                setIsDropdownOpen(false);\n                            // Navigate to profile\n                            },\n                            className: \"block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n                                            lineNumber: 131,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n                                        lineNumber: 130,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"პროფილი\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n                                        lineNumber: 133,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n                                lineNumber: 129,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n                            lineNumber: 122,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                setIsDropdownOpen(false);\n                            // Navigate to my trips\n                            },\n                            className: \"block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n                                                lineNumber: 146,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n                                                lineNumber: 147,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n                                        lineNumber: 145,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"ჩემი გასვლები\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n                                        lineNumber: 149,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n                                lineNumber: 144,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n                            lineNumber: 137,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                setIsDropdownOpen(false);\n                            // Navigate to settings\n                            },\n                            className: \"block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n                                                lineNumber: 162,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n                                                lineNumber: 163,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n                                        lineNumber: 161,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"პარამეტრები\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n                                        lineNumber: 165,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n                                lineNumber: 160,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n                            lineNumber: 153,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-200 dark:border-gray-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleLogout,\n                                className: \"block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 dark:text-red-400 dark:hover:bg-red-900/20 transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n                                                lineNumber: 176,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n                                            lineNumber: 175,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"გასვლა\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n                                            lineNumber: 178,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n                                    lineNumber: 174,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n                                lineNumber: 170,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n                            lineNumber: 169,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n                    lineNumber: 109,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n                lineNumber: 108,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/components/UserProfile.js\",\n        lineNumber: 80,\n        columnNumber: 5\n    }, this);\n}\n_s(UserProfile, \"xHB0TPjksLfE8sNEj1mF05TVeUM=\", false, function() {\n    return [\n        _context_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = UserProfile;\nvar _c;\n$RefreshReg$(_c, \"UserProfile\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/UserProfile.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/context/AuthContext.js":
/*!************************************!*\
  !*** ./app/context/AuthContext.js ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\nfunction useAuth() {\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (!context) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s1();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Check for existing user session on mount\n            const savedUser = localStorage.getItem('user');\n            if (savedUser) {\n                try {\n                    setUser(JSON.parse(savedUser));\n                } catch (error) {\n                    console.error('Error parsing saved user:', error);\n                    localStorage.removeItem('user');\n                }\n            }\n            setIsLoading(false);\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const login = async (email, password)=>{\n        try {\n            // Simulate API call\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            // Mock user data - in real app, this would come from your backend\n            const userData = {\n                id: 1,\n                email,\n                name: 'Test User',\n                phone: '+995555123456',\n                avatar: null,\n                isVerified: true\n            };\n            setUser(userData);\n            localStorage.setItem('user', JSON.stringify(userData));\n            return {\n                success: true\n            };\n        } catch (error) {\n            return {\n                success: false,\n                error: 'Login failed'\n            };\n        }\n    };\n    const register = async (userData)=>{\n        try {\n            // Simulate API call\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            // Mock registration - in real app, this would create user in backend\n            const newUser = {\n                id: Date.now(),\n                ...userData,\n                avatar: null,\n                isVerified: false // Will be true after phone verification\n            };\n            setUser(newUser);\n            localStorage.setItem('user', JSON.stringify(newUser));\n            return {\n                success: true,\n                user: newUser\n            };\n        } catch (error) {\n            return {\n                success: false,\n                error: 'Registration failed'\n            };\n        }\n    };\n    const logout = ()=>{\n        setUser(null);\n        localStorage.removeItem('user');\n    };\n    const updateUser = (updates)=>{\n        const updatedUser = {\n            ...user,\n            ...updates\n        };\n        setUser(updatedUser);\n        localStorage.setItem('user', JSON.stringify(updatedUser));\n    };\n    const verifyPhone = async (code)=>{\n        try {\n            // Simulate phone verification\n            await new Promise((resolve)=>setTimeout(resolve, 1500));\n            // Mock verification success\n            const updatedUser = {\n                ...user,\n                isVerified: true\n            };\n            setUser(updatedUser);\n            localStorage.setItem('user', JSON.stringify(updatedUser));\n            return {\n                success: true\n            };\n        } catch (error) {\n            return {\n                success: false,\n                error: 'Verification failed'\n            };\n        }\n    };\n    const value = {\n        user,\n        isLoading,\n        login,\n        register,\n        logout,\n        updateUser,\n        verifyPhone,\n        isAuthenticated: !!user\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/augment-projects/paranormal/app/context/AuthContext.js\",\n        lineNumber: 115,\n        columnNumber: 5\n    }, this);\n}\n_s1(AuthProvider, \"YajQB7LURzRD+QP5gw0+K2TZIWA=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/context/AuthContext.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fgiorgi%2FDocuments%2Faugment-projects%2Fparanormal%2Fapp%2Fcomponents%2FHeroSection.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgiorgi%2FDocuments%2Faugment-projects%2Fparanormal%2Fapp%2Fcomponents%2FNavigation.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgiorgi%2FDocuments%2Faugment-projects%2Fparanormal%2Fapp%2Fcomponents%2FPhotoShowcase.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgiorgi%2FDocuments%2Faugment-projects%2Fparanormal%2Fapp%2Fcomponents%2FTripsSection.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fgiorgi%2FDocuments%2Faugment-projects%2Fparanormal%2Fapp%2Fcomponents%2FHeroSection.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgiorgi%2FDocuments%2Faugment-projects%2Fparanormal%2Fapp%2Fcomponents%2FNavigation.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgiorgi%2FDocuments%2Faugment-projects%2Fparanormal%2Fapp%2Fcomponents%2FPhotoShowcase.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgiorgi%2FDocuments%2Faugment-projects%2Fparanormal%2Fapp%2Fcomponents%2FTripsSection.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/HeroSection.js */ \"(app-pages-browser)/./app/components/HeroSection.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/Navigation.js */ \"(app-pages-browser)/./app/components/Navigation.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/PhotoShowcase.js */ \"(app-pages-browser)/./app/components/PhotoShowcase.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/TripsSection.js */ \"(app-pages-browser)/./app/components/TripsSection.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fgiorgi%2FDocuments%2Faugment-projects%2Fparanormal%2Fapp%2Fcomponents%2FHeroSection.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgiorgi%2FDocuments%2Faugment-projects%2Fparanormal%2Fapp%2Fcomponents%2FNavigation.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgiorgi%2FDocuments%2Faugment-projects%2Fparanormal%2Fapp%2Fcomponents%2FPhotoShowcase.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgiorgi%2FDocuments%2Faugment-projects%2Fparanormal%2Fapp%2Fcomponents%2FTripsSection.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!\n"));

/***/ })

});