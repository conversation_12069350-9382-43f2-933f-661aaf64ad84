{"name": "paranormal", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "init-db": "node scripts/init-db.js", "test-auth": "node scripts/test-auth.js"}, "dependencies": {"bcryptjs": "^3.0.2", "dotenv": "^16.5.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "mysql2": "^3.14.1", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@tailwindcss/postcss": "^4", "tailwindcss": "^4"}}